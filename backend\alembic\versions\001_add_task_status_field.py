"""添加任务状态字段

Revision ID: 001_add_task_status_field
Revises: 0dad6fe84a7b
Create Date: 2025-01-08 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001_add_task_status_field'
down_revision = '0dad6fe84a7b'
branch_labels = None
depends_on = None


def upgrade():
    """添加任务状态字段"""
    # 添加 task_status 字段，默认值为 'enabled'
    op.add_column('chaos_tasks', sa.Column('task_status', sa.String(length=20), nullable=False, server_default='enabled', comment='任务状态：enabled/disabled'))
    
    # 更新现有记录的注释，明确运行状态的含义
    op.alter_column('chaos_tasks', 'status', comment='运行状态：pending/running/completed/failed/cancelled')


def downgrade():
    """移除任务状态字段"""
    # 移除 task_status 字段
    op.drop_column('chaos_tasks', 'task_status')
    
    # 恢复原来的注释
    op.alter_column('chaos_tasks', 'status', comment='任务状态：pending/running/paused/completed/failed/cancelled')

"""创建数据工厂相关表

Revision ID: 001_data_factory
Revises: 
Create Date: 2024-01-30 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '001_data_factory'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """创建数据工厂相关表"""
    
    # 创建数据模型表
    op.create_table(
        'data_factory_models',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('name', sa.String(100), nullable=False, comment='模型名称（唯一标识）'),
        sa.Column('description', sa.Text(), nullable=True, comment='模型描述'),
        sa.Column('version', sa.String(20), nullable=True, default='1.0.0', comment='版本号'),
        sa.Column('category', sa.String(50), nullable=True, comment='模型分类'),
        sa.Column('tags', sa.JSON(), nullable=True, comment='标签列表'),
        sa.Column('fields_config', sa.JSO<PERSON>(), nullable=False, comment='字段配置JSON'),
        sa.Column('usage_count', sa.Integer(), nullable=True, default=0, comment='使用次数'),
        sa.Column('status', sa.String(10), nullable=True, default='1', comment='状态：1-启用，2-禁用'),
        sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
        sa.Column('created_by', sa.String(50), nullable=True, comment='创建者'),
        sa.Column('updated_by', sa.String(50), nullable=True, comment='更新者'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name'),
        comment='数据工厂-数据模型表'
    )
    
    # 创建索引
    op.create_index('ix_data_factory_models_name', 'data_factory_models', ['name'])
    op.create_index('ix_data_factory_models_category', 'data_factory_models', ['category'])
    op.create_index('ix_data_factory_models_status', 'data_factory_models', ['status'])
    op.create_index('ix_data_factory_models_created_at', 'data_factory_models', ['created_at'])
    
    # 创建数据生成任务表
    op.create_table(
        'data_factory_generation_tasks',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('name', sa.String(100), nullable=False, comment='任务名称'),
        sa.Column('description', sa.Text(), nullable=True, comment='任务描述'),
        sa.Column('model_id', sa.Integer(), nullable=False, comment='数据模型ID'),
        sa.Column('record_count', sa.Integer(), nullable=False, comment='生成数据条数'),
        sa.Column('export_format', sa.String(20), nullable=True, default='json', comment='导出格式：json/csv/excel/sql'),
        sa.Column('export_config', sa.JSON(), nullable=True, comment='导出配置JSON'),
        sa.Column('status', sa.String(20), nullable=True, default='pending', comment='任务状态：pending/running/completed/failed/cancelled'),
        sa.Column('progress', sa.Integer(), nullable=True, default=0, comment='执行进度（0-100）'),
        sa.Column('started_at', sa.DateTime(), nullable=True, comment='开始执行时间'),
        sa.Column('completed_at', sa.DateTime(), nullable=True, comment='完成时间'),
        sa.Column('execution_time', sa.Integer(), nullable=True, comment='执行耗时（秒）'),
        sa.Column('result_file_path', sa.String(500), nullable=True, comment='结果文件路径'),
        sa.Column('result_file_size', sa.Integer(), nullable=True, comment='结果文件大小（字节）'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
        sa.Column('memory_usage', sa.Integer(), nullable=True, comment='内存使用量（MB）'),
        sa.Column('cpu_usage', sa.Integer(), nullable=True, comment='CPU使用率（%）'),
        sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
        sa.Column('created_by', sa.String(50), nullable=True, comment='创建者'),
        sa.Column('updated_by', sa.String(50), nullable=True, comment='更新者'),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['model_id'], ['data_factory_models.id'], ondelete='CASCADE'),
        comment='数据工厂-数据生成任务表'
    )
    
    # 创建索引
    op.create_index('ix_data_factory_generation_tasks_model_id', 'data_factory_generation_tasks', ['model_id'])
    op.create_index('ix_data_factory_generation_tasks_status', 'data_factory_generation_tasks', ['status'])
    op.create_index('ix_data_factory_generation_tasks_created_at', 'data_factory_generation_tasks', ['created_at'])
    op.create_index('ix_data_factory_generation_tasks_started_at', 'data_factory_generation_tasks', ['started_at'])
    op.create_index('ix_data_factory_generation_tasks_completed_at', 'data_factory_generation_tasks', ['completed_at'])


def downgrade():
    """删除数据工厂相关表"""
    
    # 删除索引
    op.drop_index('ix_data_factory_generation_tasks_completed_at', 'data_factory_generation_tasks')
    op.drop_index('ix_data_factory_generation_tasks_started_at', 'data_factory_generation_tasks')
    op.drop_index('ix_data_factory_generation_tasks_created_at', 'data_factory_generation_tasks')
    op.drop_index('ix_data_factory_generation_tasks_status', 'data_factory_generation_tasks')
    op.drop_index('ix_data_factory_generation_tasks_model_id', 'data_factory_generation_tasks')
    
    op.drop_index('ix_data_factory_models_created_at', 'data_factory_models')
    op.drop_index('ix_data_factory_models_status', 'data_factory_models')
    op.drop_index('ix_data_factory_models_category', 'data_factory_models')
    op.drop_index('ix_data_factory_models_name', 'data_factory_models')
    
    # 删除表
    op.drop_table('data_factory_generation_tasks')
    op.drop_table('data_factory_models')

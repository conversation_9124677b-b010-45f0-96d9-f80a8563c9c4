"""添加混沌测试模块数据表

Revision ID: 0dad6fe84a7b
Revises: ba394c2bbf02
Create Date: 2025-07-28 21:28:08.427234

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '0dad6fe84a7b'
down_revision: Union[str, None] = 'ba394c2bbf02'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chaos_scenarios',
    sa.Column('name', sa.String(length=100), nullable=False, comment='场景名称'),
    sa.Column('fault_type', sa.String(length=50), nullable=False, comment='故障类型：cpu/memory/network/disk/process/k8s'),
    sa.Column('description', sa.Text(), nullable=True, comment='场景描述'),
    sa.Column('default_params', sa.JSON(), nullable=False, comment='默认参数配置'),
    sa.Column('param_schema', sa.JSON(), nullable=False, comment='参数结构定义和验证规则'),
    sa.Column('is_builtin', sa.Boolean(), nullable=True, comment='是否内置模板'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('category', sa.String(length=50), nullable=True, comment='场景分类'),
    sa.Column('usage_count', sa.Integer(), nullable=True, comment='使用次数'),
    sa.Column('tags', sa.String(length=500), nullable=True, comment='场景标签，逗号分隔'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建者'),
    sa.Column('updated_by', sa.String(length=50), nullable=True, comment='更新者'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chaos_scenarios_fault_type'), 'chaos_scenarios', ['fault_type'], unique=False)
    op.create_index(op.f('ix_chaos_scenarios_id'), 'chaos_scenarios', ['id'], unique=False)
    op.create_index(op.f('ix_chaos_scenarios_name'), 'chaos_scenarios', ['name'], unique=False)
    op.create_table('chaos_tasks',
    sa.Column('name', sa.String(length=100), nullable=False, comment='任务名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='任务描述'),
    sa.Column('env_id', sa.Integer(), nullable=False, comment='环境ID'),
    sa.Column('host_ids', sa.JSON(), nullable=False, comment='目标主机ID列表'),
    sa.Column('fault_type', sa.String(length=50), nullable=False, comment='故障类型：cpu/memory/network/disk/process/k8s'),
    sa.Column('fault_params', sa.JSON(), nullable=False, comment='故障参数配置'),
    sa.Column('execution_type', sa.String(length=20), nullable=True, comment='执行类型：immediate/scheduled/periodic/cron'),
    sa.Column('scheduled_time', sa.DateTime(), nullable=True, comment='定时执行时间'),
    sa.Column('periodic_config', sa.JSON(), nullable=True, comment='周期性执行配置'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='任务状态：pending/running/paused/completed/failed/cancelled'),
    sa.Column('execution_result', sa.JSON(), nullable=True, comment='执行结果汇总'),
    sa.Column('auto_destroy', sa.Boolean(), nullable=True, comment='是否自动销毁故障'),
    sa.Column('max_duration', sa.Integer(), nullable=True, comment='最大执行时长(秒)'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建者'),
    sa.Column('updated_by', sa.String(length=50), nullable=True, comment='更新者'),
    sa.ForeignKeyConstraint(['env_id'], ['environment.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chaos_tasks_id'), 'chaos_tasks', ['id'], unique=False)
    op.create_index(op.f('ix_chaos_tasks_name'), 'chaos_tasks', ['name'], unique=False)
    op.create_table('chaos_executions',
    sa.Column('task_id', sa.Integer(), nullable=False, comment='任务ID'),
    sa.Column('host_id', sa.Integer(), nullable=False, comment='执行主机ID'),
    sa.Column('host_info', sa.JSON(), nullable=True, comment='主机信息快照'),
    sa.Column('start_time', sa.DateTime(), nullable=True, comment='开始时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='结束时间'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='执行状态：pending/running/success/failed/cancelled'),
    sa.Column('chaos_uid', sa.String(length=100), nullable=True, comment='ChaosBlade执行UID'),
    sa.Column('command', sa.Text(), nullable=True, comment='执行的ChaosBlade命令'),
    sa.Column('blade_version', sa.String(length=50), nullable=True, comment='ChaosBlade版本'),
    sa.Column('output', sa.Text(), nullable=True, comment='执行输出'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('exit_code', sa.Integer(), nullable=True, comment='命令退出码'),
    sa.Column('fault_config', sa.JSON(), nullable=True, comment='故障配置快照'),
    sa.Column('duration_seconds', sa.Integer(), nullable=True, comment='执行时长(秒)'),
    sa.Column('retry_count', sa.Integer(), nullable=True, comment='重试次数'),
    sa.Column('is_auto_destroyed', sa.Boolean(), nullable=True, comment='是否已自动销毁'),
    sa.Column('destroy_time', sa.DateTime(), nullable=True, comment='销毁时间'),
    sa.Column('destroy_output', sa.Text(), nullable=True, comment='销毁命令输出'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建者'),
    sa.Column('updated_by', sa.String(length=50), nullable=True, comment='更新者'),
    sa.ForeignKeyConstraint(['task_id'], ['chaos_tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chaos_executions_chaos_uid'), 'chaos_executions', ['chaos_uid'], unique=False)
    op.create_index(op.f('ix_chaos_executions_id'), 'chaos_executions', ['id'], unique=False)
    op.alter_column('model_config', 'platform',
               existing_type=mysql.VARCHAR(length=50),
               comment='所属平台（local/deepseek/qwen等）',
               existing_comment='所属平台（openai/claude/local等）',
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('model_config', 'platform',
               existing_type=mysql.VARCHAR(length=50),
               comment='所属平台（openai/claude/local等）',
               existing_comment='所属平台（local/deepseek/qwen等）',
               existing_nullable=False)
    op.drop_index(op.f('ix_chaos_executions_id'), table_name='chaos_executions')
    op.drop_index(op.f('ix_chaos_executions_chaos_uid'), table_name='chaos_executions')
    op.drop_table('chaos_executions')
    op.drop_index(op.f('ix_chaos_tasks_name'), table_name='chaos_tasks')
    op.drop_index(op.f('ix_chaos_tasks_id'), table_name='chaos_tasks')
    op.drop_table('chaos_tasks')
    op.drop_index(op.f('ix_chaos_scenarios_name'), table_name='chaos_scenarios')
    op.drop_index(op.f('ix_chaos_scenarios_id'), table_name='chaos_scenarios')
    op.drop_index(op.f('ix_chaos_scenarios_fault_type'), table_name='chaos_scenarios')
    op.drop_table('chaos_scenarios')
    # ### end Alembic commands ###

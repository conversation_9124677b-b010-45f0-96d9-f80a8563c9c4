"""merge_heads

Revision ID: 39e3e9368bb1
Revises: 001_data_factory, 09bbff4bd635
Create Date: 2025-08-01 21:53:48.665370

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '39e3e9368bb1'
down_revision: Union[str, None] = ('001_data_factory', '09bbff4bd635')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass

"""Add cron_expression field to chaos_tasks

Revision ID: 80fbf7e5f7c2
Revises: 39e3e9368bb1
Create Date: 2025-08-02 00:19:22.923917

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '80fbf7e5f7c2'
down_revision: Union[str, None] = '39e3e9368bb1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加cron_expression字段到chaos_tasks表
    op.add_column('chaos_tasks', sa.Column('cron_expression', sa.String(100), nullable=True, comment='Cron表达式'))


def downgrade() -> None:
    # 删除cron_expression字段
    op.drop_column('chaos_tasks', 'cron_expression')

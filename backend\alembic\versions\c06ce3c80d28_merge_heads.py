"""merge heads

Revision ID: c06ce3c80d28
Revises: 001_add_task_status_field, create_schedule_tables
Create Date: 2025-08-02 04:03:57.029041

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c06ce3c80d28'
down_revision: Union[str, None] = ('001_add_task_status_field', 'create_schedule_tables')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass

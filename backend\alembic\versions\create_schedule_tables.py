"""Create schedule_jobs and schedule_task_history tables

Revision ID: create_schedule_tables
Revises: 80fbf7e5f7c2
Create Date: 2025-08-02 02:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = 'create_schedule_tables'
down_revision: Union[str, None] = '80fbf7e5f7c2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 创建schedule_jobs表 - 用于存储调度器中的作业信息
    op.create_table(
        'schedule_jobs',
        sa.Column('id', sa.String(191), nullable=False, comment='作业ID'),
        sa.Column('name', sa.String(255), nullable=False, comment='作业名称'),
        sa.Column('func', sa.String(500), nullable=False, comment='执行函数路径'),
        sa.Column('args', sa.JSON(), nullable=True, comment='函数参数'),
        sa.Column('kwargs', sa.J<PERSON>(), nullable=True, comment='函数关键字参数'),
        sa.Column('trigger', sa.String(50), nullable=False, comment='触发器类型：date/interval/cron'),
        sa.Column('trigger_args', sa.JSON(), nullable=True, comment='触发器参数'),
        sa.Column('next_run_time', sa.TIMESTAMP(), nullable=True, comment='下次运行时间'),
        sa.Column('job_state', sa.LargeBinary(), nullable=True, comment='作业状态数据'),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), comment='创建时间'),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间'),
        
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_schedule_jobs_next_run_time', 'next_run_time'),
        sa.Index('idx_schedule_jobs_trigger', 'trigger'),
        comment='调度作业表'
    )
    
    # 创建schedule_task_history表 - 用于记录定时任务执行历史
    op.create_table(
        'schedule_task_history',
        sa.Column('id', sa.BigInteger(), nullable=False, autoincrement=True, comment='主键ID'),
        sa.Column('task_id', sa.String(191), nullable=False, comment='任务ID'),
        sa.Column('task_name', sa.String(255), nullable=False, comment='任务名称'),
        sa.Column('task_type', sa.String(50), nullable=False, comment='任务类型'),
        sa.Column('function_path', sa.String(500), nullable=False, comment='执行函数路径'),
        sa.Column('function_args', sa.JSON(), nullable=True, comment='函数参数'),
        sa.Column('function_kwargs', sa.JSON(), nullable=True, comment='函数关键字参数'),
        sa.Column('trigger_type', sa.String(50), nullable=False, comment='触发器类型：date/interval/cron'),
        sa.Column('scheduled_time', sa.TIMESTAMP(), nullable=True, comment='计划执行时间'),
        sa.Column('actual_start_time', sa.TIMESTAMP(), nullable=True, comment='实际开始时间'),
        sa.Column('actual_end_time', sa.TIMESTAMP(), nullable=True, comment='实际结束时间'),
        sa.Column('duration_seconds', sa.Integer(), nullable=True, comment='执行耗时（秒）'),
        sa.Column('status', sa.String(20), nullable=False, default='running', comment='执行状态：running/success/failed/timeout'),
        sa.Column('result', sa.JSON(), nullable=True, comment='执行结果'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
        sa.Column('exception_traceback', sa.Text(), nullable=True, comment='异常堆栈'),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), comment='创建时间'),
        
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_schedule_task_history_task_id', 'task_id'),
        sa.Index('idx_schedule_task_history_status', 'status'),
        sa.Index('idx_schedule_task_history_scheduled_time', 'scheduled_time'),
        sa.Index('idx_schedule_task_history_created_at', 'created_at'),
        comment='定时任务执行历史表'
    )


def downgrade() -> None:
    # 删除表
    op.drop_table('schedule_task_history')
    op.drop_table('schedule_jobs')

"""
角色管理 API 路由
"""
from typing import Any, Dict, List
from fastapi import APIRouter, Depends
from app.core.dependencies import DatabaseDep
from app.core.exceptions import BusinessException, exception_handler
from app.api.deps import get_current_active_user, get_current_superuser
from app.models.user.user import User
from app.models.user.role import Role
from app.core.responses import response_builder

router = APIRouter()


@router.get("/list", response_model=List[Dict[str, Any]], summary="获取角色列表")
async def get_role_list(
    db: DatabaseDep,
    current_user: User = Depends(get_current_active_user)
) -> List[Dict[str, Any]]:
    """
    获取角色列表
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        角色列表数据
    """
    try:
        from sqlalchemy.future import select
        
        # 查询所有激活的角色
        result = await db.execute(
            select(Role).where(Role.is_active == True).order_by(Role.created_at.asc())
        )
        roles = result.scalars().all()
        
        # 转换为响应格式
        role_list = []
        for role in roles:
            role_item = {
                "id": role.id,
                "roleCode": role.code,
                "roleName": role.name,
                "description": role.description or "",
                "status": "1" if role.is_active else "2",
                "createTime": role.created_at.strftime("%Y-%m-%d %H:%M:%S") if role.created_at else "",
                "updateTime": role.updated_at.strftime("%Y-%m-%d %H:%M:%S") if role.updated_at else ""
            }
            role_list.append(role_item)
        
        return response_builder.success(role_list)
    except Exception as e:
        raise exception_handler.to_http_exception(
            BusinessException(message=f"获取角色列表失败: {str(e)}")
        ) 
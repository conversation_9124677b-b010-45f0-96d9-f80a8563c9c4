"""
服务层基类
提供通用的业务逻辑处理、事务管理和异常处理
"""
import asyncio
import time
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Type, Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.dependencies import ServiceDependency
from app.repositories.base import BaseRepository
from app.core.exceptions import raise_validation_error, raise_not_found
from app.schemas.common import SearchParams, PaginationParams
from app.schemas.base import PaginationResponse
from app.utils.logger import get_business_logger, operation_logger

# 泛型类型定义
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ResponseSchemaType = TypeVar("ResponseSchemaType", bound=BaseModel)


class BaseService(ServiceDependency, Generic[ModelType, CreateSchemaType, UpdateSchemaType, ResponseSchemaType], ABC):
    """
    服务层基类
    """
    
    def __init__(self, db: AsyncSession, repository: BaseRepository):
        super().__init__(db)
        self.repository = repository
        # 初始化服务辅助工具
        from app.utils.service_helpers import ServiceHelper
        self.helper = ServiceHelper(db)
        # 初始化业务日志记录器
        self.business_logger = get_business_logger(self._get_module_name())
    
    @property
    @abstractmethod
    def model_class(self) -> Type[ModelType]:
        """返回模型类"""
        pass
    
    @property
    @abstractmethod
    def response_schema_class(self) -> Type[ResponseSchemaType]:
        """返回响应Schema类"""
        pass

    def _get_module_name(self) -> str:
        """获取模块名称，用于日志记录"""
        class_name = self.__class__.__name__.lower()
        if 'user' in class_name:
            return 'user'
        elif 'env' in class_name or 'environment' in class_name:
            return 'env'
        elif 'model' in class_name:
            return 'model'
        elif 'chaos' in class_name:
            return 'chaos'
        else:
            return 'unknown'

    def _get_user_info(self, current_user: str) -> tuple[Optional[int], Optional[str]]:
        """从current_user字符串中提取用户ID和用户名"""
        try:
            # 假设current_user是用户ID的字符串形式
            user_id = int(current_user) if current_user.isdigit() else None
            # 这里可以根据实际情况获取用户名，暂时使用ID作为用户名
            username = f"user_{user_id}" if user_id else current_user
            return user_id, username
        except (ValueError, AttributeError):
            return None, current_user
    
    # ==================== 通用CRUD操作 ====================
    
    async def create(
        self,
        create_data: CreateSchemaType,
        current_user: str,
        **kwargs
    ) -> ResponseSchemaType:
        """
        通用创建方法 - 带日志记录
        """
        start_time = time.time()
        user_id, username = self._get_user_info(current_user)

        try:
            # 执行创建前验证
            await self._validate_before_create(create_data, **kwargs)

            # 准备创建数据
            create_dict = create_data.model_dump()
            create_dict.update({
                'created_by': current_user,
                'updated_by': current_user,
                **kwargs
            })

            # 执行创建前处理
            create_dict = await self._process_before_create(create_dict)

            # 直接创建模型实例，避免Schema验证问题
            db_obj = self.model_class(**create_dict)
            self.repository.db.add(db_obj)
            await self.repository.db.commit()
            await self.repository.db.refresh(db_obj)
            created_obj = db_obj

            # 执行创建后处理
            await self._process_after_create(created_obj, create_data)

            # 记录成功日志
            duration_ms = (time.time() - start_time) * 1000
            await self.business_logger.log_create(
                target_name=getattr(created_obj, 'name', f"{self.model_class.__name__}#{created_obj.id}"),
                user_id=user_id,
                username=username,
                target_id=created_obj.id,
                data=create_dict,
                duration_ms=duration_ms
            )

            # 转换为响应对象
            if hasattr(self, '_convert_to_response') and asyncio.iscoroutinefunction(self._convert_to_response):
                return await self._convert_to_response(created_obj)
            else:
                return self._convert_to_response(created_obj)

        except Exception as e:
            # 记录失败日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation=f"{self._get_module_name()}_create",
                user_id=user_id,
                username=username,
                request_data=create_data.model_dump(),
                result="failed",
                error_msg=str(e),
                duration_ms=duration_ms
            )
            raise
    
    async def get_by_id(self, obj_id: int) -> ResponseSchemaType:
        """
        根据ID获取对象
        """
        obj = await self.repository.get(obj_id)
        if not obj:
            raise_not_found(f"{self.model_class.__name__} ID {obj_id} 不存在")
        
        # 转换为响应对象
        if hasattr(self, '_convert_to_response') and asyncio.iscoroutinefunction(self._convert_to_response):
            return await self._convert_to_response(obj)
        else:
            return self._convert_to_response(obj)
    
    async def update(
        self,
        obj_id: int,
        update_data: UpdateSchemaType,
        current_user: str,
        **kwargs
    ) -> ResponseSchemaType:
        """
        通用更新方法 - 带日志记录
        """
        start_time = time.time()
        user_id, username = self._get_user_info(current_user)

        try:
            # 检查对象是否存在
            obj = await self.repository.get(obj_id)
            if not obj:
                raise_not_found(f"{self.model_class.__name__} ID {obj_id} 不存在")

            # 执行更新前验证
            await self._validate_before_update(obj, update_data, **kwargs)

            # 准备更新数据
            update_dict = update_data.model_dump(exclude_unset=True)
            update_dict.update({
                'updated_by': current_user,
                **kwargs
            })

            # 执行更新前处理
            update_dict = await self._process_before_update(obj, update_dict)

            # 更新对象
            updated_obj = await self.repository.update(db_obj=obj, obj_in=update_dict)

            # 执行更新后处理
            await self._process_after_update(updated_obj, update_data)

            # 记录成功日志
            duration_ms = (time.time() - start_time) * 1000
            await self.business_logger.log_update(
                target_name=getattr(updated_obj, 'name', f"{self.model_class.__name__}#{updated_obj.id}"),
                user_id=user_id,
                username=username,
                target_id=updated_obj.id,
                changes=update_dict,
                duration_ms=duration_ms
            )

            # 转换为响应对象
            if hasattr(self, '_convert_to_response') and asyncio.iscoroutinefunction(self._convert_to_response):
                return await self._convert_to_response(updated_obj)
            else:
                return self._convert_to_response(updated_obj)

        except Exception as e:
            # 记录失败日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation=f"{self._get_module_name()}_update",
                user_id=user_id,
                username=username,
                request_data={"obj_id": obj_id, "update_data": update_data.model_dump()},
                result="failed",
                error_msg=str(e),
                duration_ms=duration_ms
            )
            raise
    
    async def delete(self, obj_id: int, current_user: str = "system") -> None:
        """
        通用删除方法 - 带日志记录
        """
        start_time = time.time()
        user_id, username = self._get_user_info(current_user)

        try:
            # 检查对象是否存在
            obj = await self.repository.get(obj_id)
            if not obj:
                raise_not_found(f"{self.model_class.__name__} ID {obj_id} 不存在")

            # 记录删除前的对象信息
            target_name = getattr(obj, 'name', f"{self.model_class.__name__}#{obj.id}")

            # 执行删除前验证
            await self._validate_before_delete(obj)

            # 执行删除前处理
            await self._process_before_delete(obj)

            # 删除对象
            await self.repository.delete(id=obj.id)

            # 执行删除后处理
            await self._process_after_delete(obj)

            # 记录成功日志
            duration_ms = (time.time() - start_time) * 1000
            await self.business_logger.log_delete(
                target_name=target_name,
                user_id=user_id,
                username=username,
                target_id=obj_id,
                duration_ms=duration_ms
            )

        except Exception as e:
            # 记录失败日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation=f"{self._get_module_name()}_delete",
                user_id=user_id,
                username=username,
                request_data={"obj_id": obj_id},
                result="failed",
                error_msg=str(e),
                duration_ms=duration_ms
            )
            raise
    
    async def list_with_pagination(
        self,
        params: SearchParams
    ) -> PaginationResponse[ResponseSchemaType]:
        """
        分页查询列表
        """
        # 构建查询条件
        filters = await self._build_list_filters(params)
        
        # 执行分页查询
        records, total = await self.repository.get_multi_with_total(
            skip=(params.current - 1) * params.size,
            limit=params.size,
            filters=filters
        )
        
        # 转换为响应对象列表
        if hasattr(self, '_convert_to_response') and asyncio.iscoroutinefunction(self._convert_to_response):
            response_records = await asyncio.gather(*[self._convert_to_response(record) for record in records])
        else:
            response_records = [self._convert_to_response(record) for record in records]
        
        return PaginationResponse(
            records=response_records,
            total=total,
            current=params.current,
            size=params.size
        )
    
    # ==================== 钩子方法（子类可重写） ====================
    
    async def _validate_before_create(self, create_data: CreateSchemaType, **kwargs) -> None:
        """创建前验证（子类可重写）"""
        pass
    
    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前处理（子类可重写）"""
        return create_dict
    
    async def _process_after_create(self, created_obj: ModelType, create_data: CreateSchemaType) -> None:
        """创建后处理（子类可重写）"""
        pass
    
    async def _validate_before_update(self, obj: ModelType, update_data: UpdateSchemaType, **kwargs) -> None:
        """更新前验证（子类可重写）"""
        pass
    
    async def _process_before_update(self, obj: ModelType, update_dict: Dict[str, Any]) -> Dict[str, Any]:
        """更新前处理（子类可重写）"""
        return update_dict
    
    async def _process_after_update(self, updated_obj: ModelType, update_data: UpdateSchemaType) -> None:
        """更新后处理（子类可重写）"""
        pass
    
    async def _validate_before_delete(self, obj: ModelType) -> None:
        """删除前验证（子类可重写）"""
        pass
    
    async def _process_before_delete(self, obj: ModelType) -> None:
        """删除前处理（子类可重写）"""
        pass
    
    async def _process_after_delete(self, obj: ModelType) -> None:
        """删除后处理（子类可重写）"""
        pass
    
    async def _build_list_filters(self, params: SearchParams) -> Dict[str, Any]:
        """构建列表查询过滤条件（子类可重写）"""
        filters = {}
        if params.keyword:
            # 默认搜索逻辑，子类可重写
            filters['keyword'] = params.keyword
        return filters
    
    def _convert_to_response(self, obj: ModelType) -> ResponseSchemaType:
        """
        将模型对象转换为响应对象
        """
        return self.response_schema_class.model_validate(obj)
    
    # ==================== 事务管理 ====================
    
    async def execute_in_transaction(self, func, *args, **kwargs):
        """
        在事务中执行函数
        """
        async with self.db.begin():
            return await func(*args, **kwargs)


class ConfigurableService(BaseService[ModelType, CreateSchemaType, UpdateSchemaType, ResponseSchemaType]):
    """
    可配置的服务基类
    
    适用于需要配置驱动的服务，如环境管理服务
    """
    
    def __init__(self, db: AsyncSession, repository: BaseRepository, config: Optional[Dict[str, Any]] = None):
        super().__init__(db, repository)
        self.config = config or {}
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
    
    def set_config_value(self, key: str, value: Any) -> None:
        """设置配置值"""
        self.config[key] = value

"""
混沌测试故障场景业务服务
"""
from typing import Dict, Any, List, Optional, Type
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.chaos.chaos_scenario_repository import ChaosScenarioRepository
from app.models.chaos.chaos_scenario import ChaosScenario
from app.schemas.chaos.chaos_scenario import (
    ChaosScenarioCreate, ChaosScenarioUpdate, ChaosScenarioResponse, ChaosScenarioListResponse,
    ChaosScenarioForTaskResponse, ChaosScenarioStatistics, ChaosScenarioValidateRequest,
    ChaosScenarioValidateResponse, ChaosScenarioQuery, ChaosScenarioPageResponse
)
from app.schemas.base import PaginationResponse
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.logger import setup_logger

logger = setup_logger()


class ChaosScenarioService(BaseService[ChaosScenario, ChaosScenarioCreate, ChaosScenarioUpdate, ChaosScenarioResponse]):
    """
    故障场景模板业务服务
    继承BaseService，提供场景模板管理的核心业务逻辑
    """

    def __init__(self, db: AsyncSession):
        self.repository = ChaosScenarioRepository(db)
        super().__init__(db, self.repository)

    @property
    def model_class(self) -> Type[ChaosScenario]:
        return ChaosScenario

    @property
    def response_schema_class(self) -> Type[ChaosScenarioResponse]:
        return ChaosScenarioResponse

    # ==================== 钩子方法实现 ====================

    async def _validate_before_create(self, create_data: ChaosScenarioCreate, **kwargs) -> None:
        """创建前业务验证"""
        # 验证场景名称是否重复
        existing_scenario = await self.repository.get_by_name(create_data.name)
        if existing_scenario:
            raise_validation_error(f"场景名称 '{create_data.name}' 已存在")

        # 验证参数结构定义
        self._validate_param_schema(create_data.param_schema)

        # 验证默认参数与结构定义的一致性
        errors = self._validate_params_against_schema(create_data.default_params, create_data.param_schema)
        if errors:
            raise_validation_error(f"默认参数验证失败: {errors}")

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前数据处理"""
        # 设置默认值
        create_dict["is_builtin"] = False
        create_dict["is_active"] = True
        create_dict["usage_count"] = 0
        
        return create_dict

    async def _process_after_create(self, obj: ChaosScenario, create_data) -> None:
        """创建后处理"""
        logger.info(f"故障场景创建成功: {obj.name} (ID: {obj.id})")

    def _convert_to_response(self, obj: ChaosScenario) -> ChaosScenarioResponse:
        """转换为响应对象"""
        return ChaosScenarioResponse(
            id=obj.id,
            name=obj.name,
            fault_type=obj.fault_type,
            description=obj.description,
            default_params=obj.default_params or {},
            param_schema=obj.param_schema or {},
            is_builtin=obj.is_builtin,
            is_active=obj.is_active,
            category=obj.category,
            usage_count=obj.usage_count or 0,
            tags=obj.tags,
            created_at=obj.created_at,
            updated_at=obj.updated_at,
            created_by=obj.created_by,
            updated_by=obj.updated_by,
            tag_list=obj.tag_list
        )

    def _convert_to_list_response(self, obj: ChaosScenario) -> ChaosScenarioListResponse:
        """转换为列表响应对象"""
        return ChaosScenarioListResponse.model_validate(obj)

    # ==================== 业务方法 ====================

    async def list_scenarios(self, query: ChaosScenarioQuery) -> ChaosScenarioPageResponse:
        """查询场景列表，支持关键词搜索、类型筛选和分页"""
        scenarios, total = await self.repository.search_scenarios_with_query(query)

        # 转换为列表响应格式
        scenario_responses = []
        for scenario in scenarios:
            scenario_response = self._convert_to_list_response(scenario)
            scenario_responses.append(scenario_response)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ChaosScenarioPageResponse(
            items=scenario_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )


    async def validate_scenario_params(self, request: ChaosScenarioValidateRequest) -> ChaosScenarioValidateResponse:
        """验证场景参数"""
        scenario = await self.repository.get(request.scenario_id)
        if not scenario:
            raise_not_found(f"场景ID {request.scenario_id} 不存在")

        # 验证参数
        errors = scenario.validate_params(request.params)
        
        # 合并默认参数
        merged_params = scenario.merge_with_defaults(request.params)

        return ChaosScenarioValidateResponse(
            valid=len(errors) == 0,
            errors=errors,
            merged_params=merged_params
        )

    async def increment_usage(self, scenario_id: int) -> bool:
        """增加场景使用次数"""
        return await self.repository.increment_usage_count(scenario_id)

    # ==================== 私有方法 ====================

    def _validate_param_schema(self, param_schema: Dict[str, Any]) -> None:
        """验证参数结构定义"""
        if not isinstance(param_schema, dict):
            raise_validation_error("参数结构定义必须为字典格式")

        for param_name, param_def in param_schema.items():
            if not isinstance(param_def, dict):
                raise_validation_error(f"参数 {param_name} 的定义必须为字典格式")
            
            if "type" not in param_def:
                raise_validation_error(f"参数 {param_name} 必须包含type字段")

    def _validate_params_against_schema(self, params: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, str]:
        """根据结构定义验证参数"""
        errors = {}
        
        for param_name, param_def in schema.items():
            if param_def.get("required", False) and param_name not in params:
                errors[param_name] = "参数必填"
                continue
                
            if param_name in params:
                value = params[param_name]
                param_type = param_def.get("type")
                
                # 类型验证
                if param_type == "integer" and not isinstance(value, int):
                    errors[param_name] = "参数类型必须为整数"
                elif param_type == "float" and not isinstance(value, (int, float)):
                    errors[param_name] = "参数类型必须为数字"
                elif param_type == "string" and not isinstance(value, str):
                    errors[param_name] = "参数类型必须为字符串"
                
                # 范围验证
                if "min" in param_def and value < param_def["min"]:
                    errors[param_name] = f"参数值不能小于{param_def['min']}"
                if "max" in param_def and value > param_def["max"]:
                    errors[param_name] = f"参数值不能大于{param_def['max']}"
        
        return errors

    async def list_scenarios_for_task(self) -> List[ChaosScenarioForTaskResponse]:
        """
        获取任务创建时使用的场景列表，包含参数信息
        只返回启用的场景
        """
        scenarios = await self.repository.list_active_scenarios()

        # 转换为任务创建专用的响应格式
        result = []
        for scenario in scenarios:
            scenario_data = ChaosScenarioForTaskResponse(
                id=scenario.id,
                name=scenario.name,
                fault_type=scenario.fault_type,
                description=scenario.description,
                category=scenario.category,
                default_params=scenario.default_params or {},
                param_schema=scenario.param_schema or {},
                is_builtin=scenario.is_builtin,
                is_active=scenario.is_active
            )
            result.append(scenario_data)

        return result

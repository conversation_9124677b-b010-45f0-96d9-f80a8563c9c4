"""
混沌测试任务业务服务
"""
import time
from typing import Dict, Any,  Type
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.chaos.chaos_task_repository import ChaosTaskRepository
from app.repositories.chaos.chaos_execution_repository import ChaosExecutionRepository
from app.repositories.env.env import EnvironmentRepository
from app.repositories.user.user import UserRepository
from app.models.chaos.chaos_task import ChaosTask
from app.models.chaos.chaos_execution import ChaosExecution
from app.schemas.chaos.chaos_task import (
    Chaos<PERSON>ask<PERSON><PERSON>, ChaosTaskUpdate, ChaosTaskResponse, ChaosTaskListResponse,
    ChaosTaskStatistics, ChaosTaskExecuteRequest,
    ChaosTaskQuery, ChaosTaskPageResponse
)
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.logger import setup_logger, operation_logger
from .chaosblade_service import ChaosBladeService

logger = setup_logger()


class ChaosTaskService(BaseService[<PERSON>T<PERSON>, ChaosT<PERSON><PERSON><PERSON>, ChaosTaskUpdate, ChaosTaskResponse]):
    """
    混沌测试任务业务服务
    继承BaseService，提供任务管理的核心业务逻辑
    """

    def __init__(self, db: AsyncSession):
        self.repository = ChaosTaskRepository(db)
        self.execution_repository = ChaosExecutionRepository(db)
        self.env_repository = EnvironmentRepository(db)
        self.user_repository = UserRepository(db)
        self.chaosblade_service = ChaosBladeService()
        super().__init__(db, self.repository)

    @property
    def model_class(self) -> Type[ChaosTask]:
        return ChaosTask

    @property
    def response_schema_class(self) -> Type[ChaosTaskResponse]:
        return ChaosTaskResponse

    # ==================== 钩子方法实现 ====================

    async def _validate_before_create(self, create_data: ChaosTaskCreate, **kwargs) -> None:
        """创建前业务验证"""
        # 验证环境是否存在
        for env_id in create_data.env_ids:
            env = await self.env_repository.get(env_id)
            if not env:
                raise_validation_error(f"环境ID {env_id} 不存在")

        # 验证任务名称是否重复
        existing_task = await self.repository.get_by_field("name", create_data.name, unique=True)
        if existing_task:
            raise_validation_error(f"任务名称 '{create_data.name}' 已存在")

        # 验证环境ID列表
        if not create_data.env_ids:
            raise_validation_error("必须指定至少一个目标环境")

        # 验证故障参数
        self._validate_fault_params(create_data.fault_type, create_data.fault_params)

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前数据处理"""
        # 设置默认状态
        create_dict["status"] = "pending"
        create_dict["task_status"] = create_dict.get("task_status", "enabled")

        # 处理定时任务
        execution_type = create_dict.get("execution_type")
        if execution_type == "scheduled":
            if not create_dict.get("scheduled_time"):
                raise_validation_error("定时执行必须设置执行时间")
        elif execution_type == "cron":
            if not create_dict.get("cron_expression"):
                raise_validation_error("Cron执行必须设置cron表达式")
        elif execution_type == "periodic":
            if not create_dict.get("periodic_config"):
                raise_validation_error("周期执行必须设置周期配置")

        return create_dict

    async def _process_after_create(self, obj: ChaosTask, create_data: ChaosTaskCreate) -> None:
        """创建后处理"""
        logger.info(f"混沌测试任务创建成功: {obj.name} (ID: {obj.id})")

        # 如果是调度任务，创建定时任务配置
        if obj.execution_type in ["scheduled", "cron", "periodic"]:
            # 使用系统用户ID，因为 create_data 中没有 created_by 字段
            await self._create_schedule_task_config(obj, 1)

    async def _process_after_update(self, obj: ChaosTask, update_data: ChaosTaskUpdate) -> None:
        """更新后处理"""
        logger.info(f"混沌测试任务更新成功: {obj.name} (ID: {obj.id})")

        # 处理调度任务配置的更新
        if obj.execution_type in ["scheduled", "cron", "periodic"]:
            # 使用系统用户ID，因为 update_data 中没有 updated_by 字段
            await self._update_schedule_task_config(obj, 1)
        else:
            # 如果不再是调度任务，停用定时任务配置
            await self._deactivate_schedule_task_config(obj.id)

    async def _process_before_delete(self, obj: ChaosTask) -> None:
        """删除前处理"""
        # 删除定时任务配置
        await self._delete_schedule_task_config(obj.id, 0)  # 使用默认值0作为deleted_by
        logger.info(f"准备删除混沌测试任务: {obj}")

    async def _create_schedule_task_config(self, obj: ChaosTask, created_by: int):
        """创建定时任务配置"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)
            task_id = await schedule_service.create_chaos_schedule_task(
                chaos_task_id=obj.id,
                chaos_task_name=obj.name,
                execution_type=obj.execution_type,
                created_by=created_by,
                scheduled_time=obj.scheduled_time,
                cron_expression=obj.cron_expression,
                periodic_config=obj.periodic_config
            )

            logger.info(f"✅ 定时任务配置创建成功: {task_id}")

        except Exception as e:
            logger.error(f"❌ 创建定时任务配置失败: {obj.id}, 错误: {str(e)}")

    async def _update_schedule_task_config(self, obj: ChaosTask, updated_by: int):
        """更新定时任务配置"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)
            success = await schedule_service.update_chaos_schedule_task(
                chaos_task_id=obj.id,
                chaos_task_name=obj.name,
                execution_type=obj.execution_type,
                updated_by=updated_by,
                scheduled_time=obj.scheduled_time,
                cron_expression=obj.cron_expression,
                periodic_config=obj.periodic_config
            )

            if success:
                logger.info(f"✅ 定时任务配置更新成功: chaos_task_{obj.id}")
            else:
                logger.warning(f"⚠️ 定时任务配置更新失败，可能不存在: chaos_task_{obj.id}")

        except Exception as e:
            logger.error(f"❌ 更新定时任务配置失败: {obj.id}, 错误: {str(e)}")

    async def _delete_schedule_task_config(self, task_id: int, deleted_by: int = 0):
        """删除定时任务配置"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)
            success = await schedule_service.delete_chaos_schedule_task(task_id, deleted_by)

            if success:
                logger.info(f"✅ 定时任务配置删除成功: chaos_task_{task_id}")
            else:
                logger.warning(f"⚠️ 定时任务配置删除失败，可能不存在: chaos_task_{task_id}")

        except Exception as e:
            logger.error(f"❌ 删除定时任务配置失败: {task_id}, 错误: {str(e)}")

    async def _deactivate_schedule_task_config(self, task_id: int):
        """停用定时任务配置"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)
            success = await schedule_service.deactivate_chaos_schedule_task(task_id)

            if success:
                logger.info(f"✅ 定时任务配置停用成功: chaos_task_{task_id}")
            else:
                logger.warning(f"⚠️ 定时任务配置停用失败，可能不存在: chaos_task_{task_id}")

        except Exception as e:
            logger.error(f"❌ 停用定时任务配置失败: {task_id}, 错误: {str(e)}")

    async def _deactivate_schedule_task_config(self, task_id: int):
        """停用定时任务配置"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)
            success = await schedule_service.deactivate_chaos_schedule_task(task_id)

            if success:
                logger.info(f"✅ 定时任务配置停用成功: chaos_task_{task_id}")
            else:
                logger.warning(f"⚠️ 定时任务配置停用失败，可能不存在: chaos_task_{task_id}")

        except Exception as e:
            logger.error(f"❌ 停用定时任务配置失败: {task_id}, 错误: {str(e)}")

    async def _activate_or_create_schedule_task_config(self, task: ChaosTask, created_by: int):
        """激活或创建定时任务配置"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)

            # 先尝试激活已存在的配置
            success = await schedule_service.activate_chaos_schedule_task(task.id)

            if success:
                logger.info(f"✅ 定时任务配置激活成功: chaos_task_{task.id}")
            else:
                # 如果激活失败，说明配置不存在，创建新的配置
                await self._create_schedule_task_config(task, created_by)
                logger.info(f"✅ 定时任务配置创建成功: chaos_task_{task.id}")

        except Exception as e:
            logger.error(f"❌ 激活或创建定时任务配置失败: {task.id}, 错误: {str(e)}")

    async def _remove_schedule_task_config(self, task_id: int, updated_by: int):
        """移除定时任务配置"""
        try:
            from app.services.schedule.schedule_task_service import ScheduleTaskService

            schedule_service = ScheduleTaskService(self.db)
            success = await schedule_service.delete_chaos_schedule_task(task_id, updated_by)

            if success:
                logger.info(f"✅ 定时任务配置移除成功: chaos_task_{task_id}")
            else:
                logger.warning(f"⚠️ 定时任务配置移除失败，可能不存在: chaos_task_{task_id}")

        except Exception as e:
            logger.error(f"❌ 移除定时任务配置失败: {task_id}, 错误: {str(e)}")

    async def _convert_to_response(self, obj: ChaosTask) -> ChaosTaskResponse:
        """转换为响应对象（包含真实环境名称和用户昵称）"""
        # 获取环境名称列表
        env_names = []
        try:
            if obj.env_ids:
                environments = await self.env_repository.get_environments_by_ids(obj.env_ids)
                env_names = [env.name for env in environments]
                # 如果某些环境ID没有找到对应的环境，用占位符填充
                if len(env_names) < len(obj.env_ids):
                    found_ids = {env.id for env in environments}
                    for env_id in obj.env_ids:
                        if env_id not in found_ids:
                            env_names.append(f"环境{env_id}")
        except Exception:
            env_names = [f"环境{env_id}" for env_id in (obj.env_ids or [])]

        # 获取创建者和更新者昵称
        created_by_name = obj.created_by
        updated_by_name = obj.updated_by

        try:
            if obj.created_by and obj.created_by.isdigit():
                creator = await self.user_repository.get(int(obj.created_by))
                if creator:
                    created_by_name = creator.nickname or creator.username
        except Exception:
            pass

        try:
            if obj.updated_by and obj.updated_by.isdigit():
                updater = await self.user_repository.get(int(obj.updated_by))
                if updater:
                    updated_by_name = updater.nickname or updater.username
        except Exception:
            pass

        # 使用Pydantic的from_attributes自动转换
        response = ChaosTaskResponse.model_validate(obj)

        # 只设置需要特殊处理的字段
        response.created_by = created_by_name
        response.updated_by = updated_by_name
        response.environment_names = env_names
        response.env_count = len(obj.env_ids) if obj.env_ids else 0
        response.env_ids = obj.env_ids or []
        response.fault_params = obj.fault_params or {}

        # 设置状态信息
        response.can_execute = obj.can_execute
        response.can_stop = obj.can_stop
        response.can_delete = obj.can_delete
        response.can_enable = obj.can_enable
        response.can_disable = obj.can_disable

        return response

    # ==================== 业务方法 ====================

    async def list_chaos_tasks(self, query: ChaosTaskQuery) -> ChaosTaskPageResponse:
        """查询混沌测试任务列表，支持关键词搜索、状态筛选和分页"""
        tasks, total = await self.repository.search_tasks_with_query(query)

        # 转换为响应格式
        task_responses = []
        for task in tasks:
            task_response = await self._convert_to_response(task)
            task_responses.append(task_response)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ChaosTaskPageResponse(
            items=task_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )



    async def execute_task(self, task_id: int, request: ChaosTaskExecuteRequest, current_user_id: int) -> Dict[str, Any]:
        """执行混沌测试任务 - 带日志记录"""
        start_time = time.time()

        try:
            # 获取任务
            task = await self.repository.get_with_environment(task_id)
            if not task:
                raise_not_found(f"任务ID {task_id} 不存在")

            # 检查任务状态
            if not task.can_execute and not request.force:
                if not task.is_enabled:
                    raise_validation_error(f"任务已禁用，无法执行")
                else:
                    raise_validation_error(f"任务运行状态为 {task.status}，无法执行")

            # 记录任务执行开始日志
            await operation_logger.log_operation(
                operation="chaos_task_execute_start",
                user_id=current_user_id,
                username=f"user_{current_user_id}",
                request_data={
                    "task_id": task_id,
                    "task_name": task.name,
                    "fault_type": task.fault_type,
                    "env_ids": task.env_ids,
                    "monitor_enabled": bool(request.monitor_config),
                    "force_execute": request.force
                },
                result="success"
            )

            # 如果是重新执行（非pending状态），先清理之前的执行记录
            if task.status != "pending":
                logger.info(f"重新执行任务 {task_id}，清理之前的执行记录")
                # 可选：清理之前的执行记录或标记为历史记录
                # 这里我们保留历史记录，只是重新开始执行

            # 更新任务状态为运行中
            await self.repository.update_status(task_id, "running")

            # 执行任务逻辑
            # 获取所有环境的主机信息
            execution_results = []
            all_hosts = []

            # 从环境管理获取主机列表
            from app.services.env.env import EnvironmentService
            env_service = EnvironmentService(self.db)

            for env_id in task.env_ids:
                # 获取环境信息
                environment = await env_service.get_environment_by_id(env_id)
                if environment:
                    # 将环境ID作为主机ID使用（简化处理）
                    all_hosts.append({
                        "env_id": env_id,
                        "host_id": env_id,  # 使用环境ID作为主机ID
                        "host_info": {
                            "host": environment.host,
                            "port": environment.port or 22,
                            "username": environment.config.get("username", "root") if environment.config else "root",
                            "password": environment.config.get("password") if environment.config else None,
                            "private_key_path": environment.config.get("private_key_path") if environment.config else None
                        }
                    })

            # 为每个主机创建执行记录并执行
            for host_info in all_hosts:
                # 创建执行记录
                execution = ChaosExecution(
                    task_id=task_id,
                    host_id=host_info["host_id"],
                    host_info=host_info["host_info"],
                    status="pending",
                    fault_config=task.fault_params,
                    monitor_config=request.monitor_config,
                    created_by=str(current_user_id)
                )
                self.execution_repository.db.add(execution)
                await self.execution_repository.db.commit()
                await self.execution_repository.db.refresh(execution)

                # 启动监控（如果配置了）
                if request.monitor_config and request.monitor_config.get('enable_monitoring'):
                    try:
                        from app.services.chaos.chaos_monitor_service import ChaosMonitorService
                        from app.schemas.chaos.chaos_monitor import ChaosMonitorConfig

                        monitor_service = ChaosMonitorService(self.db)
                        monitor_config = ChaosMonitorConfig(**request.monitor_config)

                        await monitor_service.start_monitoring(
                            execution.id,
                            host_info["host_id"],
                            host_info["host_info"],
                            monitor_config
                        )
                        logger.info(f"监控已启动: execution_id={execution.id}, host_id={host_info['host_id']}")
                    except Exception as e:
                        logger.error(f"启动监控失败: {e}")
                        # 监控启动失败不影响任务执行

                # 开始执行 - 设置开始时间
                execution.start_execution(command="准备执行故障注入")
                await self.execution_repository.db.commit()

                # 执行故障注入
                result = await self._execute_fault_on_host(task, host_info["host_info"], execution.id)
                execution_results.append(result)

            # 更新任务执行结果
            task_result = {
                "total_hosts": len(all_hosts),
                "successful_hosts": sum(1 for r in execution_results if r["success"]),
                "failed_hosts": sum(1 for r in execution_results if not r["success"]),
                "execution_time": datetime.now().isoformat()
            }

            # 检查是否有timeout参数，决定任务状态
            has_timeout = task.fault_params and task.fault_params.get("timeout")
            timeout_seconds = None
            if has_timeout:
                try:
                    timeout_seconds = int(task.fault_params.get("timeout", 0))
                except (ValueError, TypeError):
                    timeout_seconds = None

            # 根据执行结果更新任务状态
            if all(r["success"] for r in execution_results):
                if timeout_seconds and timeout_seconds > 0:
                    # 有timeout的任务，执行成功后状态为running，等待自动完成
                    final_status = "running"
                    logger.info(f"任务 {task_id} 执行成功，有timeout参数({timeout_seconds}秒)，状态设为running")

                    # 启动后台任务监控timeout完成
                    await self._schedule_task_completion(task_id, timeout_seconds)
                else:
                    # 没有timeout的任务，执行成功后直接完成
                    final_status = "completed"
                    logger.info(f"任务 {task_id} 执行成功，无timeout参数，状态设为completed")
            elif any(r["success"] for r in execution_results):
                # 部分成功，状态为running
                final_status = "running"
                if timeout_seconds and timeout_seconds > 0:
                    await self._schedule_task_completion(task_id, timeout_seconds)
            else:
                # 全部失败
                final_status = "failed"

            await self.repository.update_status(task_id, final_status, task_result)

            # 获取执行记录ID列表
            execution_ids = []
            for result in execution_results:
                if result.get("execution_id"):
                    execution_ids.append(result["execution_id"])

            # 记录任务执行成功日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="chaos_task_execute_complete",
                user_id=current_user_id,
                username=f"user_{current_user_id}",
                request_data={
                    "task_id": task_id,
                    "task_name": task.name,
                    "execution_summary": task_result,
                    "final_status": final_status
                },
                result="success",
                duration_ms=duration_ms
            )

            return {
                "success": True,
                "message": "任务执行完成",
                "task_id": task_id,
                "execution_ids": execution_ids,  # 添加执行记录ID列表
                "execution_results": execution_results,
                "summary": task_result
            }

        except Exception as e:
            # 记录任务执行失败日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="chaos_task_execute_failed",
                user_id=current_user_id,
                username=f"user_{current_user_id}",
                request_data={"task_id": task_id},
                result="failed",
                error_msg=str(e),
                duration_ms=duration_ms
            )

            logger.error(f"执行任务失败: {str(e)}")
            await self.repository.update_status(task_id, "failed", {"error": str(e)})
            raise_validation_error(f"任务执行失败: {str(e)}")

    async def stop_task(self, task_id: int, current_user_id: int) -> bool:
        """停止任务执行（只停止当前执行，循环任务下次仍会执行）- 带日志记录"""
        start_time = time.time()

        try:
            task = await self.repository.get_with_executions(task_id)
            if not task:
                raise_not_found(f"任务ID {task_id} 不存在")

            if not task.can_stop:
                raise_validation_error(f"任务运行状态为 {task.status}，无法停止")

            # 销毁所有正在运行的故障注入
            destroyed_count = 0
            for execution in task.executions:
                if execution.is_running and execution.chaos_uid:
                    await self._destroy_fault_execution(execution)
                    destroyed_count += 1

            # 更新任务状态为已取消（只是当前这次执行）
            await self.repository.update_status(task_id, "cancelled")

            # 记录任务停止成功日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="chaos_task_stop",
                user_id=current_user_id,
                username=f"user_{current_user_id}",
                request_data={
                    "task_id": task_id,
                    "task_name": task.name,
                    "destroyed_executions": destroyed_count
                },
                result="success",
                duration_ms=duration_ms
            )

            # 清理对应的自动完成调度器任务
            await self._cleanup_completion_scheduler_task(task_id)

            logger.info(f"任务 {task_id} 已停止，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"停止任务失败: {str(e)}")
            raise_validation_error(f"停止任务失败: {str(e)}")

    async def enable_task(self, task_id: int, current_user_id: int) -> bool:
        """启用任务"""
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")

        if not task.can_enable:
            raise_validation_error(f"任务已启用，无需重复操作")

        try:
            # 更新任务状态为启用
            task.task_status = "enabled"
            await self.repository.db.commit()

            # 如果是循环任务，重新激活或创建调度器配置
            if task.execution_type in ["periodic", "cron"]:
                await self._activate_or_create_schedule_task_config(task, current_user_id)

            logger.info(f"任务 {task_id} 已启用，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"启用任务失败: {str(e)}")
            raise_validation_error(f"启用任务失败: {str(e)}")

    async def disable_task(self, task_id: int, current_user_id: int) -> bool:
        """禁用任务"""
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")

        if not task.can_disable:
            raise_validation_error(f"任务已禁用，无需重复操作")

        try:
            # 更新任务状态为禁用
            task.task_status = "disabled"
            await self.repository.db.commit()

            # 如果是循环任务，停用调度器中的任务
            if task.execution_type in ["periodic", "cron"]:
                await self._deactivate_schedule_task_config(task_id)

            logger.info(f"任务 {task_id} 已禁用，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"禁用任务失败: {str(e)}")
            raise_validation_error(f"禁用任务失败: {str(e)}")

    async def reset_task(self, task_id: int, current_user_id: int) -> bool:
        """重置任务状态，允许重新执行"""
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")

        if task.status == "running":
            raise_validation_error("正在运行的任务无法重置，请先停止任务")

        try:
            # 重置任务状态为pending
            await self.repository.update_status(task_id, "pending", {"reset_time": datetime.now().isoformat()})
            logger.info(f"任务 {task_id} 已重置，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"重置任务失败: {str(e)}")
            raise_validation_error(f"重置任务失败: {str(e)}")

    async def delete_task(self, task_id: int, current_user_id: int) -> bool:
        """删除任务"""
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")

        if not task.can_delete:
            raise_validation_error(f"任务正在运行中，请先停止任务后再删除")

        try:
            # 如果是循环任务，先从调度器中移除
            if task.execution_type in ["periodic", "cron"]:
                await self._remove_schedule_task_config(task_id, current_user_id)

            # 清理自动完成调度器任务
            await self._cleanup_completion_scheduler_task(task_id)

            # 删除任务
            await self.repository.delete(task_id)
            logger.info(f"任务 {task_id} 已删除，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            raise_validation_error(f"删除任务失败: {str(e)}")

    async def get_task_statistics(self) -> ChaosTaskStatistics:
        """获取任务统计信息 """
        stats = await self.repository.get_statistics()

        # 获取最近任务
        recent_tasks, _ = await self.repository.search_tasks(
            skip=0, limit=10, order_by="created_at", desc=True
        )

        recent_task_list = []
        for task in recent_tasks:
            recent_task_list.append(ChaosTaskListResponse(
                id=task.id,
                name=task.name,
                fault_type=task.fault_type,
                status=task.status,
                env_id=task.env_id,
                environment_name=getattr(task.environment, 'name', None) if hasattr(task, 'environment') else None,
                host_count=len(task.host_ids) if task.host_ids else 0,
                execution_type=task.execution_type,
                created_at=task.created_at,
                created_by=task.created_by,
                can_execute=task.can_execute,
                can_pause=task.can_pause,
                can_terminate=task.can_terminate
            ))

        return ChaosTaskStatistics(
            total_count=stats["total_count"],
            status_stats=stats["status_stats"],
            fault_type_stats=stats["fault_type_stats"],
            recent_tasks=recent_task_list
        )

    # ==================== 私有方法 ====================

    def _validate_fault_params(self, fault_type: str, fault_params: Dict[str, Any]) -> None:
        """验证故障参数"""
        if not fault_params:
            raise_validation_error("故障参数不能为空")

        # 根据故障类型验证必需参数
        if fault_type == "cpu":
            if "cpu-percent" not in fault_params:
                raise_validation_error("CPU故障必须指定cpu-percent参数")
        elif fault_type == "memory":
            if "mem-percent" not in fault_params:
                raise_validation_error("内存故障必须指定mem-percent参数")
        # elif fault_type == "network":
        #     if "action" not in fault_params:
        #         raise_validation_error("网络故障必须指定action参数")

    async def _execute_fault_on_host(self, task: ChaosTask, host_info: Dict[str, Any], execution_id: int) -> Dict[str, Any]:
        """在指定主机上执行故障注入"""
        try:
            # 构建故障配置
            fault_config = {
                "fault_type": task.fault_type,
                **task.fault_params
            }

            # 执行故障注入
            result = await self.chaosblade_service.execute_fault(host_info, fault_config)
            
            # 获取执行记录
            execution = await self.execution_repository.get(execution_id)
            if not execution:
                return {"success": False, "error": "执行记录不存在"}

            # 更新执行记录
            if result["success"]:
                # 故障注入成功，但保持运行状态（不设置结束时间）
                execution.status = "running"  # 保持运行状态
                execution.output = result.get("output")
                execution.exit_code = result.get("exit_code", 0)

                # 更新chaos_uid和命令信息
                if result.get("chaos_uid"):
                    execution.chaos_uid = result["chaos_uid"]
                    logger.info(f"保存故障ID: {result['chaos_uid']} 到执行记录 {execution_id}")
                if result.get("command"):
                    execution.command = result["command"]
                # 保存故障配置快照
                execution.fault_config = fault_config

                await self.execution_repository.db.commit()
                await self.execution_repository.db.refresh(execution)
            else:
                # 执行失败 - 设置结束时间和错误状态
                execution.complete_execution(
                    success=False,
                    error=result.get("error"),
                    exit_code=result.get("exit_code", 1)
                )
                await self.execution_repository.db.commit()

            return {
                "host": host_info.get("host"),
                "execution_id": execution_id,
                "success": result["success"],
                "chaos_uid": result.get("chaos_uid"),
                "message": result.get("output") or result.get("error")
            }

        except Exception as e:
            logger.error(f"主机 {host_info.get('host')} 故障注入失败: {str(e)}")
            await self.execution_repository.update_execution_status(
                execution_id, "failed", error_message=str(e)
            )
            return {
                "host": host_info.get("host"),
                "execution_id": execution_id,
                "success": False,
                "error": str(e)
            }

    async def _destroy_fault_execution(self, execution: ChaosExecution) -> None:
        """销毁故障注入执行"""
        try:
            host_info = await self._get_host_info_by_execution(execution)
            result = await self.chaosblade_service.destroy_fault(host_info, execution.chaos_uid)
            
            if result["success"]:
                logger.info(f"销毁故障：{execution.chaos_uid}任务成功")

                await self.execution_repository.mark_as_destroyed(
                    execution.id, result.get("output")
                )
            else:
                logger.error(f"销毁故障注入失败: {result.get('error')}")

        except Exception as e:
            logger.error(f"销毁执行记录 {execution.id} 失败: {str(e)}")

    async def _get_host_info_by_execution(self, execution: ChaosExecution) -> Dict[str, Any]:
        """根据执行记录获取主机信息"""
        # 从执行记录的host_info字段获取主机信息
        if execution.host_info:
            return execution.host_info

        # 如果没有保存主机信息，从环境管理获取
        from app.services.env.env import EnvironmentService
        env_service = EnvironmentService(self.db)
        environment = await env_service.get_environment_by_id(execution.host_id)  # 使用host_id作为env_id

        if environment:
            return {
                "host": environment.host,
                "port": environment.port or 22,
                "username": environment.config.get("username", "root") if environment.config else "root",
                "password": environment.config.get("password") if environment.config else None,
                "private_key_path": environment.config.get("private_key_path") if environment.config else None
            }

        # 默认返回空字典
        return {}

    async def _schedule_task_completion(self, task_id: int, timeout_seconds: int) -> None:
        """使用调度器安排任务在timeout后自动完成"""
        try:
            from app.schedule.schedule_manager import schedule_manager, create_task
            from datetime import datetime, timedelta

            # 计算任务完成时间
            completion_time = datetime.now() + timedelta(seconds=timeout_seconds)

            # 创建调度器任务ID
            scheduler_task_id = f"complete_chaos_task_{task_id}"

            # 创建调度任务
            task = create_task(
                task_id=scheduler_task_id,
                name=f"自动完成混沌任务 {task_id}",
                func=complete_chaos_task_by_timeout,  # 直接使用函数对象，不是字符串
                task_type="chaos_completion",
                description=f"自动完成混沌任务 {task_id}（{timeout_seconds}秒后）",
                args=[task_id, timeout_seconds]
            )

            # 添加一次性定时任务到调度器
            schedule_manager.add_date_task(task, completion_time, replace_existing=True)

            logger.info(f"已安排任务 {task_id} 在 {completion_time} 自动完成（{timeout_seconds}秒后）")

        except Exception as e:
            logger.error(f"安排任务 {task_id} 自动完成失败: {str(e)}")
            # 如果调度器失败，回退到asyncio方案（临时兼容）
            logger.warning(f"回退到asyncio.sleep方案处理任务 {task_id}")
            await self._fallback_schedule_task_completion(task_id, timeout_seconds)

    async def _fallback_schedule_task_completion(self, task_id: int, timeout_seconds: int) -> None:
        """回退方案：使用asyncio.sleep（仅用于调度器失败时的临时兼容）"""
        import asyncio

        async def complete_task_after_timeout():
            try:
                await asyncio.sleep(timeout_seconds)
                await complete_chaos_task_by_timeout(task_id, timeout_seconds)
            except Exception as e:
                logger.error(f"任务 {task_id} 回退方案自动完成失败: {str(e)}")

        # 启动后台任务
        asyncio.create_task(complete_task_after_timeout())

    async def _cleanup_completion_scheduler_task(self, task_id: int) -> None:
        """清理任务的自动完成调度器任务"""
        try:
            from app.schedule.schedule_manager import remove_job
            scheduler_task_id = f"complete_chaos_task_{task_id}"
            remove_job(scheduler_task_id)
            logger.info(f"已清理任务 {task_id} 的自动完成调度器任务")
        except Exception as e:
            logger.warning(f"清理任务 {task_id} 的调度器任务失败: {str(e)}")


# 模块级别的函数，供调度器调用
async def complete_chaos_task_by_timeout(task_id: int, timeout_seconds: int) -> None:
    """由调度器调用的任务完成函数"""
    try:
        from app.database.connection import get_db
        from app.repositories.chaos.chaos_task_repository import ChaosTaskRepository
        from datetime import datetime

        # 获取数据库连接
        async for db in get_db():
            repository = ChaosTaskRepository(db)

            # 检查任务是否仍在运行
            task = await repository.get_with_executions(task_id)
            if not task:
                logger.warning(f"任务 {task_id} 不存在，无法自动完成")
                return

            if task.status != "running":
                logger.info(f"任务 {task_id} 状态为 {task.status}，无需自动完成")
                return

            # 完成所有运行中的执行记录
            for execution in task.executions:
                if execution.status == "running":
                    execution.complete_execution(
                        success=True,
                        output=f"故障注入已持续{timeout_seconds}秒，自动完成"
                    )
                    logger.info(f"执行记录 {execution.id} 在timeout后自动完成")

            # 更新任务状态为completed
            completion_result = {
                "completed_at": datetime.now().isoformat(),
                "completion_reason": f"Timeout reached ({timeout_seconds}s)",
                "auto_completed": True,
                "completed_by_scheduler": True
            }
            await repository.update_status(task_id, "completed", completion_result)
            logger.info(f"任务 {task_id} 在timeout({timeout_seconds}秒)后由调度器自动完成")

            # 清理对应的调度器任务
            try:
                from app.schedule.schedule_manager import remove_job
                scheduler_task_id = f"complete_chaos_task_{task_id}"
                remove_job(scheduler_task_id)
                logger.info(f"已清理调度器任务: {scheduler_task_id}")
            except Exception as e:
                logger.warning(f"清理调度器任务失败: {str(e)}")

            break  # 只需要一个数据库连接

    except Exception as e:
        logger.error(f"调度器自动完成任务 {task_id} 失败: {str(e)}")

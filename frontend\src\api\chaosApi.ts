/**
 * 混沌测试模块API接口
 */
import api from '@/utils/http'
import type { PaginationData } from '@/types/api/common'
import type {
  ChaosTask,
  ChaosTaskCreate,
  ChaosTaskUpdate,
  ChaosTaskSearchParams,
  ChaosTaskExecuteRequest,
  ChaosTaskBatchRequest,
  ChaosTaskStatistics,
  ChaosTaskPageResponse,
  ChaosScenario,
  ChaosScenarioCreate,
  ChaosScenarioUpdate,
  ChaosScenarioSearchParams,
  ChaosScenarioValidateRequest,
  ChaosScenarioImportRequest,
  ChaosScenarioPageResponse,
  ChaosExecution,
  ChaosExecutionSearchParams,
  ChaosExecutionRetryRequest,
  ChaosExecutionBatchRequest,
  ChaosBladeInstallRequest,
  ChaosBladeStatusResponse
} from '@/types/api/chaos'

// ==================== 混沌测试服务类 ====================

export class ChaosService {
  // ==================== 任务管理API ====================

  /**
   * 获取任务列表
   */
  static async getTaskList(params: ChaosTaskSearchParams = {}): Promise<ChaosTaskPageResponse> {
    const response = await api.get<ChaosTaskPageResponse>({
      url: '/api/chaos/tasks',
      params,
      useRestfulFormat: true
    })
    return response
  }

  /**
   * 创建任务
   */
  static async createTask(data: ChaosTaskCreate): Promise<ChaosTask> {
    const response = await api.post<ChaosTask>({
      url: '/api/chaos/tasks',
      params: data
    })
    return response
  }

  /**
   * 获取任务详情
   */
  static async getTaskDetail(id: number): Promise<ChaosTask> {
    const response = await api.get<ChaosTask>({
      url: `/api/chaos/tasks/${id}`
    })
    return response
  }

  /**
   * 更新任务
   */
  static async updateTask(id: number, data: ChaosTaskUpdate): Promise<ChaosTask> {
    const response = await api.put<ChaosTask>({
      url: `/api/chaos/tasks/${id}`,
      params: data
    })
    return response
  }

  /**
   * 删除任务
   */
  static async deleteTask(id: number): Promise<boolean> {
    const response = await api.del<boolean>({
      url: `/api/chaos/tasks/${id}`
    })
    return response
  }

  /**
   * 执行任务
   */
  static async executeTask(id: number, data: ChaosTaskExecuteRequest): Promise<any> {
    const response = await api.post<any>({
      url: `/api/chaos/tasks/${id}/execute`,
      params: data
    })
    return response
  }

  /**
   * 停止任务
   */
  static async stopTask(id: number): Promise<boolean> {
    const response = await api.post<boolean>({
      url: `/api/chaos/tasks/${id}/stop`
    })
    return response
  }

  /**
   * 启用任务
   */
  static async enableTask(id: number): Promise<boolean> {
    const response = await api.post<boolean>({
      url: `/api/chaos/tasks/${id}/enable`
    })
    return response
  }

  /**
   * 禁用任务
   */
  static async disableTask(id: number): Promise<boolean> {
    const response = await api.post<boolean>({
      url: `/api/chaos/tasks/${id}/disable`
    })
    return response
  }

  /**
   * 重置任务
   */
  static async resetTask(id: number): Promise<boolean> {
    const response = await api.post<boolean>({
      url: `/api/chaos/tasks/${id}/reset`
    })
    return response
  }

  /**
   * 批量操作任务
   */
  static async batchTaskOperation(data: ChaosTaskBatchRequest): Promise<any> {
    const response = await api.post<any>({
      url: '/api/chaos/tasks/batch/',
      params: data
    })
    return response
  }

  /**
   * 获取任务统计
   */
  static async getTaskStatistics(): Promise<ChaosTaskStatistics> {
    const response = await api.get<ChaosTaskStatistics>({
      url: '/api/chaos/tasks/statistics/'
    })
    return response
  }

  // ==================== 场景管理API ====================

  /**
   * 获取场景列表
   */
  static async getScenarioList(params: ChaosScenarioSearchParams = {}): Promise<ChaosScenarioPageResponse> {
    const response = await api.get<ChaosScenarioPageResponse>({
      url: '/api/chaos/scenarios',
      params,
      useRestfulFormat: true
    })
    return response
  }

  /**
   * 创建场景
   */
  static async createScenario(data: ChaosScenarioCreate): Promise<ChaosScenario> {
    const response = await api.post<ChaosScenario>({
      url: '/api/chaos/scenarios',
      params: data
    })
    return response
  }

  /**
   * 获取场景详情
   */
  static async getScenarioDetail(id: number): Promise<ChaosScenario> {
    const response = await api.get<ChaosScenario>({
      url: `/api/chaos/scenarios/${id}`
    })
    return response
  }

  /**
   * 获取任务创建用的场景列表（包含参数信息）
   */
  static async getScenariosForTask(): Promise<ChaosScenario[]> {
    const response = await api.get<ChaosScenario[]>({
      url: '/api/chaos/scenarios/for-task'
    })
    return response
  }

  /**
   * 更新场景
   */
  static async updateScenario(id: number, data: ChaosScenarioUpdate): Promise<ChaosScenario> {
    const response = await api.put<ChaosScenario>({
      url: `/api/chaos/scenarios/${id}`,
      params: data
    })
    return response
  }

  /**
   * 删除场景
   */
  static async deleteScenario(id: number): Promise<boolean> {
    const response = await api.del<boolean>({
      url: `/api/chaos/scenarios/${id}`
    })
    return response
  }


  /**
   * 验证场景参数
   */
  static async validateScenarioParams(data: ChaosScenarioValidateRequest): Promise<any> {
    const response = await api.post<any>({
      url: '/api/chaos/scenarios/validate',
      params: data
    })
    return response
  }

  /**
   * 使用场景（增加使用次数）
   */
  static async useScenario(id: number): Promise<boolean> {
    const response = await api.post<boolean>({
      url: `/api/chaos/scenarios/${id}/use`
    })
    return response
  }

  /**
   * 导入场景
   */
  static async importScenarios(data: ChaosScenarioImportRequest): Promise<any> {
    const response = await api.post<any>({
      url: '/api/chaos/scenarios/import',
      params: data
    })
    return response
  }



  /**
   * 导出场景
   */
  static async exportScenarios(scenarioIds?: number[]): Promise<any> {
    if (scenarioIds && scenarioIds.length > 0) {
      const response = await api.post<any>({
        url: '/api/chaos/scenarios/export',
        params: scenarioIds
      })
      return response
    } else {
      const response = await api.get<any>({
        url: '/api/chaos/scenarios/export/all'
      })
      return response
    }
  }

  /**
   * 获取场景统计
   */
  static async getScenarioStatistics(): Promise<any> {
    const response = await api.get<any>({
      url: '/api/chaos/scenarios/statistics'
    })
    return response
  }

  // ==================== 执行记录API ====================

  /**
   * 获取执行记录列表
   */
  static async getExecutionList(params: ChaosExecutionSearchParams = {}): Promise<PaginationData<ChaosExecution>> {
    const response = await api.get<PaginationData<ChaosExecution>>({
      url: '/api/chaos/executions',
      params,
      useRestfulFormat: true
    })
    return response
  }

  /**
   * 获取执行记录详情
   */
  static async getExecutionDetail(id: number): Promise<ChaosExecution> {
    const response = await api.get<ChaosExecution>({
      url: `/api/chaos/executions/${id}`
    })
    return response
  }

  /**
   * 删除执行记录
   */
  static async deleteExecution(id: number): Promise<boolean> {
    const response = await api.del<boolean>({
      url: `/api/chaos/executions/${id}`
    })
    return response
  }

  /**
   * 获取任务的执行记录
   */
  static async getTaskExecutions(taskId: number, page = 1, size = 20): Promise<PaginationData<ChaosExecution>> {
    const response = await api.get<PaginationData<ChaosExecution>>({
      url: `/api/chaos/executions/task/${taskId}`,
      params: { page, size }
    })
    return response
  }

  /**
   * 获取执行日志
   */
  static async getExecutionLog(id: number, logType = 'output'): Promise<any> {
    const response = await api.post<any>({
      url: `/api/chaos/executions/${id}/log`,
      data: {
        execution_id: id,
        log_type: logType
      }
    })
    return response
  }

  /**
   * 重试执行
   */
  static async retryExecution(id: number, data: ChaosExecutionRetryRequest): Promise<ChaosExecution> {
    const response = await api.post<ChaosExecution>({
      url: `/api/chaos/executions/${id}/retry`,
      params: data
    })
    return response
  }

  /**
   * 取消执行
   */
  static async cancelExecution(id: number): Promise<boolean> {
    const response = await api.post<boolean>({
      url: `/api/chaos/executions/${id}/cancel`
    })
    return response
  }

  /**
   * 批量操作执行记录
   */
  static async batchExecutionOperation(data: ChaosExecutionBatchRequest): Promise<any> {
    const response = await api.post<any>({
      url: '/api/chaos/executions/batch',
      params: data
    })
    return response
  }

  /**
   * 获取执行统计
   */
  static async getExecutionStatistics(taskId?: number): Promise<any> {
    const params = taskId ? { task_id: taskId } : {}
    const response = await api.get<any>({
      url: '/api/chaos/executions/statistics-data',
      params,
      useRestfulFormat: true
    })
    return response
  }


  // ==================== ChaosBlade管理API ====================

  /**
   * 安装ChaosBlade
   */
  static async installChaosBlade(data: ChaosBladeInstallRequest, useSftp: boolean = true): Promise<any> {
    const response = await api.post<any>({
      url: `/api/chaos/blade/install?use_sftp=${useSftp}`,
      data: data,
      silentError: true  // 使用静默错误处理
    })
    return response
  }

  /**
   * 获取Linux主机列表
   */
  static async getLinuxHosts(): Promise<any[]> {
    const response = await api.get<any[]>({
      url: '/api/chaos/blade/hosts'
    })
    return response
  }

  /**
   * 检查所有Linux主机的ChaosBlade状态
   */
  static async checkAllChaosBladeStatus(quickCheck: boolean = true): Promise<ChaosBladeStatusResponse[]> {
    const response = await api.get<ChaosBladeStatusResponse[]>({
      url: '/api/chaos/blade/status',
      params: { quick_check: quickCheck },
      silentError: true  // 使用静默错误处理
    })
    return response
  }

  /**
   * 检查ChaosBlade状态（保留兼容性）
   */
  static async checkChaosBladeStatus(envId: number): Promise<ChaosBladeStatusResponse[]> {
    const response = await api.get<ChaosBladeStatusResponse[]>({
      url: `/api/chaos/blade/status/${envId}`
    })
    return response
  }

  /**
   * 测试ChaosBlade连接
   */
  static async testChaosBladeConnection(envId: number): Promise<any> {
    const response = await api.post<any>({
      url: `/api/chaos/blade/test-connection/${envId}`,
      silentError: true  // 使用静默错误处理
    })
    return response
  }

  // /**
  //  * 执行测试命令 - 已废弃，前端不再使用
  //  */
  // static async executeTestCommand(hostInfo: any, faultConfig: any): Promise<any> {
  //   const response = await api.post<any>({
  //     url: '/api/chaos/blade/execute-test',
  //     params: { host_info: hostInfo, fault_config: faultConfig }
  //   })
  //   return response
  // }

  /**
   * 获取ChaosBlade版本信息
   */
  static async getChaosBladeVersion(): Promise<any> {
    const response = await api.get<any>({
      url: '/api/chaos/blade/version'
    })
    return response
  }

  /**
   * 卸载ChaosBlade
   */
  static async uninstallChaosBlade(data: ChaosBladeInstallRequest): Promise<any> {
    const response = await api.post<any>({
      url: '/api/chaos/blade/uninstall',
      data: data,
      silentError: true  // 使用静默错误处理
    })
    return response
  }
}

// 默认导出ChaosService类
export default ChaosService



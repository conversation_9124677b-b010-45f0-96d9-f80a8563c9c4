<template>
  <div class="basic-info-step">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="step-form"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input
          :model-value="formData.name"
          @update:model-value="(val) => formData.name = val"
          placeholder="请输入任务名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="任务描述" prop="description">
        <el-input
          :model-value="formData.description"
          @update:model-value="(val) => formData.description = val"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="目标环境" prop="env_ids">
        <el-select
          :model-value="formData.env_ids"
          @update:model-value="(val) => formData.env_ids = val"
          multiple
          placeholder="请选择目标环境"
          style="width: 100%"
          :loading="environmentsLoading"
        >
          <el-option
            v-for="env in environments"
            :key="env.id"
            :label="env.name"
            :value="env.id"
          >
            <span>{{ env.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ env.description }}
            </span>
          </el-option>
        </el-select>
        <div class="form-tip">
          选择故障注入的目标环境，可以选择多个环境
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { EnvironmentResponse } from '@/types/api/environment'

interface Props {
  formData: {
    name: string
    description?: string
    env_ids: number[]
  }
  environments: EnvironmentResponse[]
  environmentsLoading?: boolean
}

defineProps<Props>()

const formRef = ref<FormInstance>()

const rules: FormRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 100, message: '任务名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  env_ids: [
    { required: true, message: '请选择目标环境', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个环境', trigger: 'change' }
  ]
}

// 暴露验证方法
const validate = () => {
  return formRef.value?.validate()
}

defineExpose({
  validate
})
</script>

<style scoped>
.basic-info-step {
  padding: 20px 0;
}

.step-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}
</style>

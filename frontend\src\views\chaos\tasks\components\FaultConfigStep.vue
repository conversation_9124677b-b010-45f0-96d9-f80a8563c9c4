<template>
  <div class="fault-config-step">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="step-form"
    >
      <!-- 故障分类选择 -->
      <el-form-item label="故障分类" prop="fault_category">
        <el-select
          v-model="selectedCategory"
          placeholder="请选择故障分类"
          @change="handleCategoryChange"
          style="width: 300px"
        >
          <el-option
            v-for="category in faultCategories"
            :key="category.key"
            :label="category.label"
            :value="category.key"
          />
        </el-select>
      </el-form-item>

      <!-- 故障类型选择 -->
      <el-form-item
        v-if="selectedCategory"
        label="故障类型"
        prop="fault_type"
      >
        <el-radio-group v-model="formData.fault_type" @change="handleFaultTypeChange">
          <el-radio-button
            v-for="faultType in currentCategoryFaultTypes"
            :key="faultType.key"
            :value="faultType.key"
          >
            {{ faultType.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>

      <!-- 故障配置 - 左右分栏布局 -->
      <div v-if="formData.fault_type" class="fault-config-container">
        <!-- 左侧：参数配置区域 -->
        <div class="fault-params-section">
          <div class="params-header">
            <h4>故障参数配置</h4>
            <div class="params-actions">
              <el-button size="small" @click="resetParams">清空参数</el-button>
              <el-button size="small" @click="formatJson">格式化JSON</el-button>
            </div>
          </div>

          <!-- JSON编辑器 -->
          <div class="json-editor-container">
            <el-input
              v-model="jsonParams"
              type="textarea"
              :rows="12"
              placeholder="请输入JSON格式的参数配置"
              @input="handleJsonChange"
              class="json-editor"
            />

            <!-- JSON提示和示例 -->
            <div class="json-info">
              <div class="json-status">
                <el-tag v-if="jsonValid" type="success" size="small">
                  <el-icon><Check /></el-icon>
                  JSON格式正确
                </el-tag>
                <el-tag v-else type="danger" size="small">
                  <el-icon><Close /></el-icon>
                  JSON格式错误
                </el-tag>
                <span class="param-count">参数数量: {{ Object.keys(formData.fault_params).length }}</span>
              </div>
            </div>
          </div>

          <!-- 参数说明 -->
          <div v-if="selectedScenario?.param_schema && Object.keys(selectedScenario.param_schema).length > 0" class="param-schema-section">
            <div class="schema-title">
              <h6>参数说明</h6>
              <el-tag size="small" type="info">
                共 {{ Object.keys(selectedScenario?.param_schema || {}).length }} 个参数
              </el-tag>
            </div>

            <div class="schema-table">
              <div class="schema-header">
                <span class="header-name">参数名</span>
                <span class="header-required">必填</span>
                <span class="header-type">类型</span>
                <span class="header-desc">说明</span>
              </div>
              <div
                v-for="(schema, paramName) in selectedScenario?.param_schema"
                :key="paramName"
                class="schema-row"
              >
                <span class="param-name">{{ paramName }}</span>
                <span class="param-required">
                  <el-tag v-if="isRequiredParam(paramName, selectedScenario)" type="danger" size="small">必填</el-tag>
                  <el-tag v-else type="info" size="small">可选</el-tag>
                </span>
                <span class="param-type">
                  <el-tag size="small" :type="getParamTypeColor(schema.type)">
                    {{ getParamTypeLabel(schema.type) }}
                  </el-tag>
                </span>
                <span class="param-desc">{{ getParamDescription(schema) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：场景模板选择 -->
        <div class="fault-templates-section">
          <div class="templates-header">
            <h4>场景模板</h4>
          </div>

          <div class="templates-content">
            <!-- 内置模板 -->
            <div v-if="groupedScenarios.hasBuiltin" class="template-section">
              <h5>内置模板</h5>
              <div class="template-grid">
                <div
                  v-for="scenario in groupedScenarios.builtin"
                  :key="scenario.id"
                  class="template-card builtin"
                  :class="{ active: selectedScenario?.id === scenario.id }"
                  @click="handleSelectScenario(scenario)"
                >
                  <div class="template-header">
                    <span class="template-name">{{ scenario.name }}</span>
                    <div class="template-tags">
                      <el-tag size="small" type="success">内置</el-tag>
                    </div>
                  </div>
                  <div class="template-desc">{{ scenario.description }}</div>
                  <div class="template-usage">使用次数: {{ scenario.usage_count || 0 }}</div>
                </div>
              </div>
            </div>

            <!-- 自定义模板 -->
            <div v-if="groupedScenarios.hasCustom" class="template-section">
              <h5>自定义模板</h5>
              <div class="template-grid">
                <div
                  v-for="scenario in groupedScenarios.custom"
                  :key="scenario.id"
                  class="template-card custom"
                  :class="{ active: selectedScenario?.id === scenario.id }"
                  @click="handleSelectScenario(scenario)"
                >
                  <div class="template-header">
                    <span class="template-name">{{ scenario.name }}</span>
                    <div class="template-tags">
                      <el-tag size="small" type="info">自定义</el-tag>
                    </div>
                  </div>
                  <div class="template-desc">{{ scenario.description }}</div>
                  <div class="template-usage">使用次数: {{ scenario.usage_count || 0 }}</div>
                </div>
              </div>
            </div>

            <!-- 无模板提示 -->
            <div v-if="!groupedScenarios.hasBuiltin && !groupedScenarios.hasCustom" class="no-templates">
              <el-empty description="暂无可用的场景模板" />
            </div>


          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { ChaosScenario } from '@/types/api/chaos'
import { FAULT_CATEGORIES, getFaultTypeDefaultParams } from '@/constants/chaos-fault-types'
import { Check, Close } from '@element-plus/icons-vue'

interface Props {
  formData: {
    fault_type: string
    fault_params: Record<string, any>
  }
  scenarios: ChaosScenario[]
  selectedCategory: string
}

interface Emits {
  (e: 'update:selectedCategory', value: string): void
  (e: 'faultTypeChange', faultType: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()

// 选中的分类
const selectedCategory = ref('')

// 选中的场景
const selectedScenario = ref<ChaosScenario | null>(null)

// JSON参数编辑器
const jsonParams = ref('')
const jsonValid = ref(true)

// 故障分类映射
const faultCategories = computed(() => {
  return FAULT_CATEGORIES.map(category => ({
    key: category.key,
    label: category.label,
    faultTypes: category.faultTypes.map(faultType => ({
      key: faultType.key,
      label: faultType.label
    }))
  }))
})

// 当前分类下的故障类型
const currentCategoryFaultTypes = computed(() => {
  if (!selectedCategory.value) return []
  const category = faultCategories.value.find(cat => cat.key === selectedCategory.value)
  return category?.faultTypes || []
})

// 过滤后的场景
const filteredScenarios = computed(() => {
  return props.scenarios.filter(s =>
    s.fault_type === props.formData.fault_type && s.is_active
  )
})

// 按类型分组的场景（内置/自定义）
const groupedScenarios = computed(() => {
  const builtin = filteredScenarios.value.filter(s => s.is_builtin)
  const custom = filteredScenarios.value.filter(s => !s.is_builtin)

  return {
    builtin,
    custom,
    hasBuiltin: builtin.length > 0,
    hasCustom: custom.length > 0
  }
})



// 表单验证规则
const rules: FormRules = {
  fault_type: [
    { required: true, message: '请选择故障类型', trigger: 'change' }
  ]
}



// 处理分类变化
const handleCategoryChange = (category: string) => {
  selectedCategory.value = category
  // 清空故障类型选择
  props.formData.fault_type = ''
  props.formData.fault_params = {}
  emit('update:selectedCategory', category)
}

// 处理故障类型变化
const handleFaultTypeChange = (faultType: string | number | boolean | undefined) => {
  if (typeof faultType !== 'string') return

  // 使用统一定义的默认参数
  const defaultParams = getFaultTypeDefaultParams(faultType)

  // 清空现有参数
  Object.keys(props.formData.fault_params).forEach(key => {
    delete props.formData.fault_params[key]
  })

  // 设置默认参数
  Object.assign(props.formData.fault_params, defaultParams)

  emit('faultTypeChange', faultType)
}

// 根据故障类型设置分类
const setSelectedCategoryByFaultType = (faultType: string) => {
  for (const category of FAULT_CATEGORIES) {
    if (category.faultTypes.some(ft => ft.key === faultType)) {
      selectedCategory.value = category.key
      break
    }
  }
}

// JSON参数处理方法
const handleJsonChange = () => {
  try {
    const parsed = JSON.parse(jsonParams.value)
    jsonValid.value = true

    // 清空现有参数
    Object.keys(props.formData.fault_params).forEach(key => {
      delete props.formData.fault_params[key]
    })

    // 设置新参数，进行类型转换
    Object.keys(parsed).forEach(key => {
      let value = parsed[key]

      // 如果是数字字符串，转换为数字
      if (typeof value === 'string' && /^\d+(\.\d+)?$/.test(value)) {
        value = parseFloat(value)
      }

      props.formData.fault_params[key] = value
    })
  } catch (error) {
    jsonValid.value = false
  }
}

const resetParams = () => {
  jsonParams.value = '{}'
  Object.keys(props.formData.fault_params).forEach(key => {
    delete props.formData.fault_params[key]
  })
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(jsonParams.value)
    jsonParams.value = JSON.stringify(parsed, null, 2)
  } catch (error) {
    // 如果JSON无效，不做处理
  }
}

// 场景选择方法
const handleSelectScenario = async (scenario: ChaosScenario) => {
  selectedScenario.value = scenario

  try {
    // 总是获取场景详情以确保有完整的参数信息
    const { useChaosScenariosStore } = await import('@/store/business/chaos/scenarios')
    const scenariosStore = useChaosScenariosStore()
    const scenarioDetail = await scenariosStore.fetchScenario(scenario.id)

    // 更新场景数据
    Object.assign(scenario, scenarioDetail)
    selectedScenario.value = scenario // 更新选中的场景

    // 调用使用场景接口，增加使用次数
    try {
      await scenariosStore.useScenario(scenario.id)
      // 更新本地的使用次数
      if (scenario.usage_count !== undefined) {
        scenario.usage_count += 1
      }
    } catch (error) {
      console.warn('更新场景使用次数失败:', error)
    }

    // 设置参数
    if (scenarioDetail.default_params) {
      jsonParams.value = JSON.stringify(scenarioDetail.default_params, null, 2)
      handleJsonChange()
    } else {
      // 如果没有默认参数，清空编辑器
      jsonParams.value = '{}'
      handleJsonChange()
    }
  } catch (error) {
    console.error('获取场景详情失败:', error)
  }
}

// 参数说明相关工具方法
const isRequiredParam = (paramKey: string, scenario: ChaosScenario): boolean => {
  // 检查场景的required字段
  if ((scenario as any).required && Array.isArray((scenario as any).required)) {
    return (scenario as any).required.includes(paramKey)
  }

  // 如果没有required字段，检查param_schema中的required属性
  if ((scenario as any).param_schema && (scenario as any).param_schema[paramKey]) {
    return (scenario as any).param_schema[paramKey].required === true
  }

  // 默认为可选参数
  return false
}

const getParamTypeColor = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const colorMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'string': 'primary',
    'number': 'success',
    'integer': 'success',
    'boolean': 'warning',
    'array': 'info',
    'object': 'danger'
  }
  return colorMap[type] || 'primary'
}

const getParamTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'string': '字符串',
    'number': '数字',
    'integer': '整数',
    'boolean': '布尔值',
    'array': '数组',
    'object': '对象'
  }
  return labelMap[type] || type
}

const getParamDescription = (schema: any) => {
  return schema.description || schema.title || '无描述'
}



// 监听参数变化，同步到JSON编辑器
watch(() => props.formData.fault_params, (newParams) => {
  if (Object.keys(newParams).length > 0) {
    jsonParams.value = JSON.stringify(newParams, null, 2)
  }
}, { deep: true, immediate: true })

// 初始化时设置分类
if (props.formData.fault_type) {
  setSelectedCategoryByFaultType(props.formData.fault_type)
}

// 暴露验证方法
const validate = () => {
  return formRef.value?.validate()
}

defineExpose({
  validate
})
</script>

<style scoped>
.fault-config-step {
  padding: 20px 0;
}

.step-form {
  max-width: 100%;
  margin: 0 auto;
}

.scenario-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.scenario-name {
  font-weight: 500;
}

.scenario-desc {
  font-size: 12px;
  color: #909399;
}

/* 故障配置容器 - 左右分栏 */
.fault-config-container {
  display: flex;
  gap: 24px;
  margin-top: 24px;
  min-height: 600px;
}

/* 左侧参数配置区域 */
.fault-params-section {
  flex: 1;
  min-width: 0;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.params-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.params-actions {
  display: flex;
  gap: 8px;
}

.json-editor-container {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.json-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  background: var(--el-bg-color);
}

.json-info {
  background: var(--el-bg-color-page);
  border-top: 1px solid var(--el-border-color);
  padding: 12px;
}

.json-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.param-count {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

/* 右侧模板选择区域 */
.fault-templates-section {
  width: 420px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.templates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.templates-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.templates-content {
  max-height: 520px;
  overflow-y: auto;
  padding-right: 4px;
}

.template-section {
  margin-bottom: 24px;
}

.template-section h5 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
  padding: 8px 12px;
  background: var(--el-fill-color-extra-light);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary);
}

.template-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.template-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--el-bg-color);
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--el-color-primary-light-9), transparent);
  transition: width 0.3s ease;
  z-index: 0;
}

.template-card:hover::before {
  width: 100%;
}

.template-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.template-card.active {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
}

.template-card.active::before {
  width: 100%;
  background: linear-gradient(90deg, var(--el-color-primary-light-8), transparent);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

.template-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
  line-height: 1.4;
}

.template-tags {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.template-desc {
  color: var(--el-text-color-regular);
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

.template-usage {
  color: var(--el-text-color-secondary);
  font-size: 11px;
  position: relative;
  z-index: 1;
}

.no-templates {
  text-align: center;
  padding: 60px 20px;
  color: var(--el-text-color-secondary);
}

/* 模板详情 */
.selected-scenario-detail {
  margin-top: 24px;
  border-top: 1px solid var(--el-border-color-light);
  padding-top: 16px;
}

.detail-header h5 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 500;
}

.scenario-info {
  margin-bottom: 16px;
}

.scenario-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 16px;
  margin-bottom: 8px;
}

.scenario-type {
  margin-bottom: 8px;
}

.scenario-desc {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  line-height: 1.4;
}

/* 参数说明表格 */
.param-schema-section {
  margin-top: 24px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
}

.schema-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.schema-title h6 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.schema-table {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  overflow: hidden;
  background: var(--el-bg-color);
}

.schema-header {
  display: grid;
  grid-template-columns: 1.2fr 70px 90px 2fr;
  gap: 12px;
  background: var(--el-fill-color-light);
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  font-size: 13px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.schema-row {
  display: grid;
  grid-template-columns: 1.2fr 70px 90px 2fr;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-size: 13px;
  align-items: center;
  transition: background-color 0.2s;
}

.schema-row:hover {
  background: var(--el-fill-color-extra-light);
}

.schema-row:last-child {
  border-bottom: none;
}

.param-name {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: var(--el-color-primary);
  font-weight: 600;
  font-size: 12px;
}

.param-desc {
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

/* 默认参数 */
.scenario-params {
  margin-bottom: 16px;
}

.params-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.params-title h6 {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 13px;
  font-weight: 500;
}

.params-json {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  padding: 12px;
  overflow-x: auto;
}

.params-json pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: var(--el-text-color-primary);
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.params-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.param-item {
  background: var(--el-bg-color);
  padding: 15px;
  border-radius: 6px;
  border: 1px solid var(--el-border-color);
}

.param-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}
</style>

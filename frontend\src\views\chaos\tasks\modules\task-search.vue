<template>
  <ArtSearchBar
    :filter="filter as Record<string, any>"
    :items="searchItems"
    @search="handleSearch"
    @reset="handleReset"
    @update:filter="$emit('update:filter', $event as ChaosTaskSearchParams)"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import { useEnvironmentStore } from '@/store/business/environment/index'
import type { ChaosTaskSearchParams } from '@/types/api/chaos'
import type { SearchFormItem } from '@/types/component'
import { FAULT_TYPE_OPTIONS } from '@/constants/chaos'

defineOptions({ name: 'TaskSearch' })

interface Props {
  filter: ChaosTaskSearchParams
}

interface Emits {
  (e: 'search'): void
  (e: 'reset'): void
  (e: 'update:filter', value: ChaosTaskSearchParams): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const environmentStore = useEnvironmentStore()
const environments = ref<any[]>([])

// 搜索项配置
const searchItems = ref<SearchFormItem[]>([
  {
    prop: 'keyword',
    label: '关键词',
    type: 'input' as const,
    placeholder: '搜索任务名称或描述'
  },
  {
    prop: 'env_ids',
    label: '环境',
    type: 'select' as const,
    placeholder: '选择环境',
    multiple: true,
    options: () => environments.value
  },
  {
    prop: 'fault_type',
    label: '故障类型',
    type: 'select' as const,
    placeholder: '选择故障类型',
    options: FAULT_TYPE_OPTIONS
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    placeholder: '选择状态',
    options: [
      { label: '待执行', value: 'pending' },
      { label: '运行中', value: 'running' },
      { label: '已暂停', value: 'paused' },
      { label: '已完成', value: 'completed' },
      { label: '已失败', value: 'failed' },
      { label: '已取消', value: 'cancelled' }
    ]
  },
  {
    prop: 'execution_type',
    label: '执行类型',
    type: 'select',
    placeholder: '选择执行类型',
    options: [
      { label: '立即执行', value: 'immediate' },
      { label: '定时执行', value: 'scheduled' },
      { label: '周期执行', value: 'periodic' },
      { label: 'Cron执行', value: 'cron' }
    ]
  }
])

onMounted(async () => {
  try {
    const result = await environmentStore.fetchEnvironments()
    if (result) {
      environments.value = result.items.map(env => ({
        label: env.name,
        value: env.id
      }))
    }
  } catch (error) {
    console.error('加载环境列表失败:', error)
  }
})

const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}
</script>

<template>
  <div class="data-factory-models art-full-height">
    <!-- 搜索栏 -->
    <ModelSearch v-model:filter="defaultFilter" @reset="resetSearch" @search="handleSearch" />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton @click="handleCreate" type="primary">创建模型</ElButton>
          <ElButton
            v-if="selectedRows.length > 0"
            @click="handleBatchDelete"
            :loading="isLoading"
          >
            批量删除
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        @selection-change="handleSelectionChange"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
      </ArtTable>



      <!-- 数据预览对话框 -->
      <DataPreviewDialog
        v-model:visible="previewDialog.visible"
        :model-info="previewDialog.modelInfo"
        :preview-data="previewDialog.previewData"
        :loading="previewDialog.loading"
        @refresh="handleRefreshPreview"
      />

      <!-- 复制模型对话框 -->
      <DuplicateModelDialog
        v-model:visible="duplicateDialog.visible"
        :source-model="duplicateDialog.sourceModel"
        @confirm="handleConfirmDuplicate"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { useWindowSize } from '@vueuse/core'
import { ElMessage, ElMessageBox, ElTag, ElCard, ElButton } from 'element-plus'
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
import ArtTable from '@/components/core/tables/art-table/index.vue'
import ArtTableHeader from '@/components/core/tables/art-table-header/index.vue'
import { useTable } from '@/composables/useTable'
import { DataFactoryService } from '@/api/dataFactoryApi'
import { formatDateTime } from '@/utils'
import ModelSearch from './components/ModelSearch.vue'
import DataPreviewDialog from './components/DataPreviewDialog.vue'
import DuplicateModelDialog from './components/DuplicateModelDialog.vue'

defineOptions({ name: 'DataFactoryModels' })

const router = useRouter()
const { width } = useWindowSize()
const selectedRows = ref<Api.DataFactory.ModelInfo[]>([])

// 默认搜索条件
const defaultFilter = ref({
  keyword: '',
  category: '',
  status: ''
})

// 预览对话框
const previewDialog = reactive({
  visible: false,
  modelInfo: null as Api.DataFactory.ModelInfo | null,
  previewData: [] as any[],
  loading: false
})

// 复制对话框
const duplicateDialog = reactive({
  visible: false,
  sourceModel: null as Api.DataFactory.ModelInfo | null
})

/**
 * API适配函数 - 将数据模型API适配为useTable需要的格式
 */
const getDataModelsForTable = async (params: any) => {
  const response = await DataFactoryService.getDataModels(params)
  return {
    records: response.records,
    total: response.total,
    current: response.current,
    size: response.size
  }
}

/**
 * 使用 useTable 管理表格数据
 */
const {
  columns,
  columnChecks,
  tableData,
  isLoading,
  paginationState,
  searchData,
  resetSearch,
  onPageSizeChange,
  onCurrentPageChange,
  refreshAll
} = useTable<Api.DataFactory.ModelInfo>({
  // 核心配置
  core: {
    apiFn: getDataModelsForTable,
    apiParams: {
      page: 1,
      size: 20,
      ...defaultFilter.value
    },
    columnsFactory: () => [
      { type: 'selection' }, // 勾选列
      { type: 'index', width: 60, label: '序号' }, // 序号
      {
        prop: 'name',
        label: '模型名称',
        minWidth: width.value < 500 ? 180 : 150,
        formatter: (row) => {
          return h('div', { class: 'model-name' }, [
            h('div', { style: 'font-weight: 500; margin-bottom: 4px; display: flex; align-items: center; gap: 8px;' }, [
              h('span', row.name),
              row.version ? h(ElTag, { type: 'info', size: 'small' }, () => row.version) : null
            ]),
            h('div', {
              style: 'font-size: 12px; color: #999; line-height: 1.2;'
            }, row.description || '-')
          ])
        }
      },
      {
        prop: 'category',
        label: '分类',
        width: 120,
        formatter: (row) => {
          return row.category ? h(ElTag, { size: 'small' }, () => row.category) : h('span', { style: 'color: #999;' }, '-')
        }
      },
      {
        prop: 'fields_config',
        label: '字段数',
        width: 100,
        formatter: (row) => {
          return h(ElTag, { type: 'success', size: 'small' }, () => row.fields_config?.length || 0)
        }
      },
      {
        prop: 'usage_count',
        label: '使用次数',
        width: 100,
        formatter: (row) => {
          return row.usage_count || 0
        }
      },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        formatter: (row) => {
          return h(ElTag, {
            type: row.status === '1' ? 'success' : 'danger',
            size: 'small'
          }, () => row.status === '1' ? '启用' : '禁用')
        }
      },
      {
        prop: 'created_at',
        label: '创建时间',
        width: 180,
        formatter: (row) => {
          return formatDateTime(row.created_at)
        }
      },
      {
        prop: 'operation',
        label: '操作',
        width: 200,
        fixed: 'right',
        formatter: (row) =>
          h('div', { style: 'display: flex; gap: 8px;' }, [
            h(ArtButtonTable, {
              type: 'view',
              onClick: () => handlePreview(row)
            }),
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => handleEdit(row)
            }),
            h(ArtButtonTable, {
              type: 'more',
              onClick: () => handleDuplicate(row)
            }),
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDelete(row)
            })
          ])
      }
    ]
  }
})

// 搜索处理
const handleSearch = () => {
  searchData({ ...defaultFilter.value })
  refreshAll()
}



// 事件处理
const handleCreate = () => {
  router.push('/data-factory/models/create')
}

const handleEdit = (row: Api.DataFactory.ModelInfo) => {
  router.push(`/data-factory/models/${row.id}/edit`)
}

const handlePreview = async (row: Api.DataFactory.ModelInfo) => {
  previewDialog.modelInfo = row
  previewDialog.visible = true
  await handleRefreshPreview()
}

const handleRefreshPreview = async () => {
  if (!previewDialog.modelInfo) return

  try {
    previewDialog.loading = true
    const response = await DataFactoryService.previewDataModel(previewDialog.modelInfo.id, 5)
    previewDialog.previewData = response.preview_data
  } catch (error) {
    ElMessage.error('预览数据失败')
  } finally {
    previewDialog.loading = false
  }
}

const handleDuplicate = (row: Api.DataFactory.ModelInfo) => {
  duplicateDialog.sourceModel = row
  duplicateDialog.visible = true
}

const handleConfirmDuplicate = async (newName: string) => {
  if (!duplicateDialog.sourceModel) return

  try {
    await DataFactoryService.duplicateDataModel(duplicateDialog.sourceModel.id, newName)
    ElMessage.success('模型复制成功')
    duplicateDialog.visible = false
    refreshAll()
  } catch (error) {
    ElMessage.error('模型复制失败')
  }
}

const handleDelete = async (row: Api.DataFactory.ModelInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DataFactoryService.deleteDataModel(row.id)
    ElMessage.success('删除成功')
    refreshAll()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的模型')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个模型吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除逻辑
    const deletePromises = selectedRows.value.map(model =>
      DataFactoryService.deleteDataModel(model.id)
    )

    await Promise.all(deletePromises)
    ElMessage.success('批量删除成功')
    refreshAll()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

/**
 * 处理表格行选择变化
 */
const handleSelectionChange = (selection: Api.DataFactory.ModelInfo[]): void => {
  selectedRows.value = selection
}
</script>

<style scoped lang="scss">
.data-factory-models {
  .model-name {
    .name {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }
}
</style>
